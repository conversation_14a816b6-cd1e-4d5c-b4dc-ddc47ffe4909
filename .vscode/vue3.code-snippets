{
  "Print towere Vue3 SFC": {
    "scope": "vue",
    "prefix": "v3",
    "body": [
      "<route lang=\"json5\" type=\"page\">",
      "{",
      "  layout: 'default',",
      "  style: {",
      "    navigationBarTitleText: '$1',",
      "  },",
      "}",
      "</route>\n",
      "<script lang=\"ts\" setup>",
      "//$3",
      "</script>\n",
      "<template>",
      "  <view class=\"\">$2</view>",
      "</template>\n",
      "<style lang=\"scss\" scoped>",
      "//$4",
      "</style>\n",
    ],
  },
  "Print towere style": {
    "scope": "vue",
    "prefix": "st",
    "body": [
      "<style lang=\"scss\" scoped>",
      "//",
      "</style>\n"
    ],
  },
  "Print towere script": {
    "scope": "vue",
    "prefix": "sc",
    "body": [
      "<script lang=\"ts\" setup>",
      "//$3",
      "</script>\n"
    ],
  },
  "Print towere template": {
    "scope": "vue",
    "prefix": "te",
    "body": [
      "<template>",
      "  <view class=\"\">$1</view>",
      "</template>\n"
    ],
  },
}