import type { IHomeModuleInfoVo } from './types/home'
import { essPrefix, jsonFlowPrefix } from '@/config/constant'
import { http } from '@/utils/http'

// 查询首页模块配置
export function getHomeModuleList(data: IHomeModuleInfoVo) {
  return http({
    url: `${essPrefix}/indexCfgInfo/queryModuleIcon`,
    method: 'post',
    data,
  })
}

// 查询首页待办
export function querySummaryIndex() {
  return http({
    url: `${jsonFlowPrefix}/runFlowExt/querySummaryIndex`,
    method: 'get',
  })
}
