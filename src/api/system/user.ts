import { eduUser } from '@/config/constant'
import { http } from '@/utils/http'

// 查询用户个人信息
export function getUserProfileAPI() {
  return http.get(`${eduUser}/system/user/profile`)
}

// 用户头像上传
export function uploadAvatar(data) {
  return http.post(`${eduUser}/system/user/profile/avatar`, data, null, { 'Content-Type': 'application/x-www-form-urlencoded' })
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return http.put(`${eduUser}/system/user/profile`, data)
}
