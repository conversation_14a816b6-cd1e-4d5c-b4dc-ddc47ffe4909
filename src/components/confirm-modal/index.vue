<script lang="ts" setup>
import { ref } from 'vue'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})

const props = defineProps({
  title: {
    type: String,
    default: '系统提示',
  },
  confirmText: {
    type: String,
    default: '确认',
  },
  cancelText: {
    type: String,
    default: '取消',
  },
  showCancel: {
    type: Boolean,
    default: true,
  },
})
const emits = defineEmits(['confirm', 'cancel'])
const visible = ref(false)
const content = ref('')
function open(e: string) {
  content.value = e
  visible.value = true
}
function handleConfirm() {
  visible.value = false
  emits('confirm')
}
function handleCancel() {
  visible.value = false
  emits('cancel')
}
// 暴露方法给父组件
defineExpose({
  open,
})
</script>

<template>
  <wd-overlay :show="visible" :z-index="999" @click="visible = false">
    <view class="wrapper">
      <!-- 移除transition，直接用class控制动画 -->
      <view v-show="visible" class="block" :class="{ 'modal-animate': visible }" @click.stop="">
          <div class="confirm-modal-box">
            <div class="confirm-modal-content">
              <div class="confirm-modal-title">
                {{ title }}
              </div>
              <image
                src="@/static/images/icon/confirm-img.png"
                mode="widthFix"
                class="confirm-img"
              />
              <div class="confirm-text">
                {{ content }}
              </div>
              <div class="confirm-modal-actions">
                <wd-button
                  v-if="showCancel"
                  type="info"
                  size="large"
                  @click="handleCancel"
                >
                  {{ cancelText }}
                </wd-button>
                <wd-button
                  type="primary"
                  size="large"
                  @click="handleConfirm"
                >
                  {{ confirmText }}
                </wd-button>
              </div>
            </div>
          </div>
        </view>
    </view>
  </wd-overlay>
</template>

<style lang="scss" scoped>
.wrapper {
  @apply flex-center w-full h-full;
}
.block {
  @apply flex-center w-auto h-auto;
}
.confirm-modal-box {
  background: #fff;
  border-radius: 16rpx;
  width: 80vw;
  max-width: 780rpx;
  padding: 55rpx 40rpx;
  position: relative;
  box-sizing: border-box;
  background: linear-gradient(180deg, #cff7eaf3 0%, #ffffff 15%);
  .confirm-modal-content {
    text-align: left;
    .confirm-modal-title {
      font-size: 34rpx;
      font-weight: bold;
      color: #0c1433;
    }
    .confirm-img {
      @apply absolute! right-30rpx top-[-35rpx] w-220rpx h-220rpx;
    }
    .confirm-text {
      @apply m-y-40rpx color-#38383a text-28rpx leading-45rpx;
    }
    .confirm-modal-actions {
      @apply flex-x-center gap-20rpx;
      :deep(.wd-button) {
        width: 100%;
        height: 86rpx;
      }
    }
  }
}
</style>
