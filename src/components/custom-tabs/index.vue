<script setup name="CustomTabs">
const props = defineProps({
  tabOptions: {
    type: Array,
    default: () => [],
  },
  modelValue: {
    type: [Number, String],
    default: 0,
  },
})

const tab = ref(props.modelValue)

const flexWidth = computed(() => {
  return `${100 / props.tabOptions.length - 5}%`
})

const emit = defineEmits(["update:modelValue", "change"])
watch(
  () => tab.value,
  (val) => {
    emit("update:modelValue", val)
  }
)

const changeTab = (e) => {
  emit("change", e)
}
</script>

<script>
export default {
  options: {
    styleIsolation: "shared",
  },
}
</script>

<template>
  <div class="custom-tabs">
    <wd-tabs v-model="tab" inactive-color="#0C1433" @change="changeTab">
      <block v-for="item in tabOptions" :key="item.value">
        <wd-tab :title="item.label">
          <slot></slot>
        </wd-tab>
      </block>
    </wd-tabs>
  </div>
</template>

<style scoped lang="scss">
.custom-tabs {
  :deep(.wd-tabs) {
    background: transparent;
    .wd-tabs__nav {
      background: transparent;
      padding-left: 20rpx;
    }
    .wd-tabs__line {
      bottom: 2rpx;
    }
  }
  :deep(.wd-tabs__nav-item) {
    flex: 0 0 v-bind(flexWidth);
    font-size: 28rpx;
    &.is-active {
      font-size: 32rpx;
      color: $uni-color-primary;
    }
  }
}
</style>
