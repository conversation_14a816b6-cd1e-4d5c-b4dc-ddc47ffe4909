<script setup>
const props = defineProps({
  title: {
    type: String,
    default: '暂无数据',
  },
})
</script>

<template>
  <div class="empty-list-box m-t-[-200rpx] color-#84AD9E">
    <image
      src="@/static/images/list-empty.png"
      mode="widthFix"
      class="w-400rpx"
    />
    <div class="m-t-[-40rpx]">
      {{ title }}
    </div>
  </div>
</template>

<style scoped  lang="scss">
.empty-list-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>
