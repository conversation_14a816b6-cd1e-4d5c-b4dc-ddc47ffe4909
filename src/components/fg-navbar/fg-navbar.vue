<script lang="ts" setup>
defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})

const props = withDefaults(
  defineProps<{
    leftText?: string
    rightText?: string
    leftArrow?: boolean
    bordered?: boolean
    fixed?: boolean
    placeholder?: boolean
    zIndex?: number
    safeAreaInsetTop?: boolean
    leftDisabled?: boolean
    rightDisabled?: boolean
    customLeft?: boolean
    leftClick?: any
  }>(),
  {
    leftText: '',
    rightText: '',
    leftArrow: true,
    bordered: false,
    fixed: false,
    placeholder: true,
    zIndex: 1,
    safeAreaInsetTop: true,
    leftDisabled: false,
    rightDisabled: false,
    customLeft: false,
  },
)

const emit = defineEmits(['leftClick'])
function handleClickLeft() {
  if (props.leftClick) {
    props.leftClick()
  }
  else {
    uni.navigateBack({
      fail() {
        uni.reLaunch({
          url: '/pages/index/index',
        })
      },
    })
  }
}
</script>

<template>
  <view class="nav-container">
    // #ifdef H5
    <image class="bg" src="@/static/images/navbar-bg-short.png" mode="widthFix" />
    // #endif // #ifndef H5
    <image class="bg" src="@/static/images/navbar-bg.png" mode="widthFix" />
    // #endif
    <wd-navbar
      :left-text="leftText"
      :right-text="rightText"
      :left-arrow="leftArrow"
      :bordered="bordered"
      :fixed="fixed"
      :placeholder="placeholder"
      :z-index="zIndex"
      :safe-area-inset-top="safeAreaInsetTop"
      :left-disabled="leftDisabled"
      :right-disabled="rightDisabled"
      class="fg-navbar"
      @click-left="handleClickLeft"
    >
      <template #title>
        <slot />
      </template>
    </wd-navbar>
  </view>
</template>

<style lang="scss" scoped>
.nav-container {
  position: sticky;
  top: 0;
  z-index: 99;
  .bg {
    width: 100%;
    position: absolute;
    height: 100%;
    left: 0;
    top: 0;
  }
  :deep(.wd-navbar) {
    background: transparent;
  }
}
</style>
