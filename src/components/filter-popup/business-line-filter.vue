<template>
  <!-- 通用筛选弹窗，数据和选中项由本组件管理 -->
  <FilterPopup
    v-model:show="innerShow"
    :groups="businessLineGroups"
    v-model:checkedId="innerCheckedId"
    :fieldMap="fieldMap || { group: 'groupName', items: 'items', label: 'businessLineName', value: 'id' }"
    :multiple="multiple"
    @confirm="onConfirm"
    @reset="onReset"
  />
</template>

<script lang="ts" setup>
import { ref, watch, computed } from "vue"
import FilterPopup from "./index.vue"
import { useBusinessLines } from "./useBusinessLines"

// 组件props定义
// show: 控制弹窗显示
// checkedId: 当前选中的id（支持单选/多选）
// fieldMap: 字段映射，适配不同数据结构
// multiple: 是否多选
const props = defineProps<{
  show: boolean
  checkedId?: string | number | Array<string | number> | null
  fieldMap?: {
    group: string
    label: string
    value: string
    items?: string
  }
  multiple?: boolean
}>()
// 组件事件定义
// update:show、update:checkedId 用于v-model
// confirm: 确定时回调
// reset: 重置时回调
const emit = defineEmits(["update:show", "update:checkedId", "confirm", "reset"])

// 使用 hooks 获取业务线数据
const { businessLines, loading } = useBusinessLines()

// 根据ascription分组的业务线数据
const businessLineGroups = computed(() => {
  const groupMap = new Map([
    [0, { groupName: '校级业务线', items: [] }],
    [1, { groupName: '二级业务线', items: [] }]
  ])

  businessLines.value.forEach(item => {
    const group = groupMap.get(+item.ascription)
    if (group) {
      group.items.push(item)
    }
  })

  // 过滤掉空的组
  return Array.from(groupMap.values()).filter(group => group.items.length > 0)
})

// 内部弹窗显示与选中状态
const innerShow = ref(props.show)
const innerCheckedId = ref(props.checkedId ?? (props.multiple ? [] : null))

// 监听外部show/checkedId变化，保持内部状态同步
watch(
  () => props.show,
  (val) => (innerShow.value = val)
)
watch(innerShow, (val) => emit("update:show", val))
watch(
  () => props.checkedId,
  (val) => (innerCheckedId.value = val)
)
watch(innerCheckedId, (val) => emit("update:checkedId", val))

// 确定事件
function onConfirm(val: any) {
  emit("confirm", val)
}
// 重置事件
function onReset() {
  emit("reset")
}
</script>

<style lang="scss" scoped></style>