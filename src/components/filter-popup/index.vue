<template>
  <view class="filter-popup">
    <slot name="trigger" :onShow="handleShow">
      <div class="drop-btn-wrap" @click="handleShow">
        <image src="@/static/images/icon/filter-icon.png" mode="widthFix" />
        <text>筛选</text>
        <wd-icon
          v-if="checkedId && checkedId.length > 0"
          name="check-circle-filled"
          size="28rpx"
          :color="themeColor"
          class="ml-8rpx"
        ></wd-icon>
      </div>
    </slot>
    <wd-popup
      v-model="innerShow"
      position="bottom"
      safe-area-inset-bottom
      closable
      @close="handleClose"
      custom-style="border-radius: 16rpx 16rpx 0 0"
      :z-index="999"
    >
      <view class="popup-content">
        <view class="text-34rpx font-600 m-30rpx">{{ title || "筛选" }}</view>
        <view class="popup-content__main">
          <view
            v-for="(group, tabIndex) in normalizedGroups"
            :key="tabIndex"
            class="group-card"
          >
            <div class="group-card__title" v-if="group[props.fieldMap.group]">
              {{ group[props.fieldMap.group] }}
            </div>
            <div class="group-card__content">
              <div
                v-for="item in group[props.fieldMap.items || 'items']"
                :key="item[props.fieldMap.value]"
                class="group-card__name"
                :class="{
                  active: props.multiple
                    ? Array.isArray(innerCheckedId) &&
                      innerCheckedId.includes(item[props.fieldMap.value])
                    : innerCheckedId === item[props.fieldMap.value],
                }"
                @click="handleClick(item)"
              >
                {{ item[props.fieldMap.label] }}
              </div>
            </div>
          </view>
        </view>
        <div class="popup-content__footer">
          <wd-button type="info" plain block @click="handleReset">重置</wd-button>
          <wd-button type="primary" block @click="handleConfirm">确定</wd-button>
        </div>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { themeColor } from "@/config/constant"
defineOptions({
  options: {
    styleIsolation: "shared",
  },
})
interface FieldMap {
  group: string // 分组名字段
  items?: string // 分组下选项数组字段（可选）
  label: string // 选项显示名字段
  value: string // 选项唯一值字段
}

const props = defineProps<{
  show: boolean
  groups: any[]
  checkedId?: string | number | Array<string | number> | null
  title?: string
  fieldMap: FieldMap
  multiple?: boolean
}>()

const emit = defineEmits(["update:show", "update:checkedId", "confirm", "reset"])

const innerShow = ref(props.show)
const innerCheckedId = ref(
  props.multiple
    ? Array.isArray(props.checkedId)
      ? [...props.checkedId]
      : []
    : props.checkedId ?? null
)

watch(
  () => props.show,
  (val) => (innerShow.value = val)
)
watch(innerShow, (val) => emit("update:show", val))
watch(
  () => props.checkedId,
  (val) => {
    console.log("watch checkedId", val)
    if (props.multiple) {
      innerCheckedId.value = Array.isArray(val) ? [...val] : []
    } else {
      innerCheckedId.value = val
    }
  }
)

// 自动分组处理
const normalizedGroups = computed(() => {
  if (
    props.groups.length &&
    props.fieldMap.items &&
    Array.isArray(props.groups[0][props.fieldMap.items])
  ) {
    // 已分组结构
    return props.groups
  }
  // 扁平结构，自动分组
  const groupMap = new Map<string, any[]>()
  props.groups.forEach((item) => {
    const groupName = item[props.fieldMap.group] || ""
    if (!groupMap.has(groupName)) {
      groupMap.set(groupName, [])
    }
    groupMap.get(groupName)!.push(item)
  })
  return Array.from(groupMap.entries()).map(([groupName, items]) => ({
    [props.fieldMap.group]: groupName,
    [props.fieldMap.items || "items"]: items,
  }))
})

function handleClose() {
  innerShow.value = false
}
function handleShow() {
  innerShow.value = true
}
function handleClick(item: any) {
  const id = item[props.fieldMap.value]
  if (props.multiple) {
    const arr = Array.isArray(innerCheckedId.value) ? [...innerCheckedId.value] : []
    const idx = arr.indexOf(id)
    if (idx > -1) {
      arr.splice(idx, 1)
    } else {
      arr.push(id)
    }
    innerCheckedId.value = arr
  } else {
    innerCheckedId.value = id
  }
}
function handleReset() {
  innerCheckedId.value = props.multiple ? [] : null
  emit("update:checkedId", innerCheckedId.value)
  emit("reset")
}
function handleConfirm() {
  handleClose()
  emit("confirm", innerCheckedId.value)
  emit("update:checkedId", innerCheckedId.value)
}
</script>

<style lang="scss" scoped>
.drop-btn-wrap {
  position: relative;
  z-index: 99;
  display: flex;
  align-items: center;
  font-size: 26rpx;
  image {
    width: 24rpx;
    height: 24rpx;
    margin-right: 10rpx;
  }
}

.popup-content {
  margin: auto;
  position: relative;
  .popup-content__main {
    height: 60vh;
    overflow-y: auto;
    padding: 10rpx 10rpx 150rpx 30rpx;
    box-sizing: border-box;
    .group-card {
      margin-bottom: 50rpx;
      .group-card__title {
        font-weight: bold;
        margin-bottom: 25rpx;
      }
      .group-card__content {
        font-size: 26rpx;
        display: flex;
        flex-wrap: wrap;
        gap: 26rpx 26rpx;
        .group-card__name {
          min-width: calc(33% - 26rpx);
          box-sizing: border-box;
          @apply rounded-full bg-#F5F5F5 p-y-14rpx px-20rpx text-center;
          border: 1px solid #f5f5f5;
          &.active {
            background: $uni-color-primary-light-8;
            border: 1px solid $uni-color-primary-light-7;
            color: var(--wot-color-theme);
          }
        }
      }
    }
  }
  &__footer {
    @apply w-full m-auto bg-#fff pb-30rpx pt-10rpx p-x-30rpx flex-x-between absolute bottom-0 left-0 right-0;
    --wot-button-medium-height: 86rpx;
    box-sizing: border-box;
    :deep(.wd-button) {
      width: 48%;
    }
  }
}
</style>
