import { ref } from "vue"
import { getBusinessLineListAPI } from "@/api/ess/baseSet/index"

const businessLines = ref<any[]>([])
const loaded = ref(false)
const loading = ref(false)

export function useBusinessLines() {
  async function fetch() {
    if (loaded.value || loading.value) return
    loading.value = true
    try {
      const res = await getBusinessLineListAPI()
      businessLines.value = Array.isArray(res.object) ? res.object : []
      loaded.value = true
    } finally {
      loading.value = false
    }
  }
  fetch()
  return { businessLines, loading }
} 