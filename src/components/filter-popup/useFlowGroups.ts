import { ref } from "vue"
import { getTodoTypeGroupAPI } from "@/api/ess/flow/todo"

const flowGroups = ref<any[]>([])
const loaded = ref(false)
const loading = ref(false)

export function useFlowGroups() {
  async function fetch() {
    if (loaded.value || loading.value) return
    loading.value = true
    try {
      const res = await getTodoTypeGroupAPI()
      flowGroups.value = Array.isArray(res.object) ? res.object : []
      loaded.value = true
    } finally {
      loading.value = false
    }
  }
  fetch()
  return { flowGroups, loading }
} 