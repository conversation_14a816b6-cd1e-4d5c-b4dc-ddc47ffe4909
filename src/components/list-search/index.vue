<template>
  <view class="search-input">
    <wd-search
      v-model="keyword"
      :placeholder="placeholder"
      hide-cancel
      placeholder-left
      :maxlength="100"
      @clear="handleQuery"
      @change="handleQuery"
    />
  </view>
</template>

<script lang="ts" setup>
import { debounce } from "@/utils/common"
defineOptions({
  options: {
    styleIsolation: "shared",
  },
})

const props = defineProps({
  modelValue: {
    type: String,
    default: null,
  },
  placeholder: {
    type: String,
    default: '请输入搜索内容',
  }
})
const keyword = ref(null)
const emit = defineEmits(["update:modelValue", "change"])
const handleQuery = () => {
  emit("update:modelValue", keyword.value)
  debouncedSearch()
}

const debouncedSearch = debounce(() => {
  emit("change", keyword.value)
}, 500)
</script>

<style lang="scss" scoped>
.search-input {
  width: 94%;
  margin: 20rpx auto;
  position: relative;
  z-index: 10;
  :deep(.wd-search) {
    @apply p-y-14rpx rounded-full bg-#ebebeb;
    .wd-search__input,
    .wd-search__block {
      background-color: #ebebeb;
    }
    .wd-search__input {
      padding-left: 10rpx;
      padding-right: 100rpx;
    }
    .wd-search__placeholder-txt {
      font-size: 28rpx;
      color: #969696;
    }
    .wd-search__clear {
      right: 50rpx;
    }
    .wd-icon-search {
      @apply right-10rpx left-auto z-99 color-#aaa text-28rpx;
    }
  }
}
</style>
