<script setup lang="ts">
defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})
// 实名推送
function handleRealAction() {
  console.log('实名推送功能')
}
</script>

<template>
  <view class="no-real-container">
    <image src="@/static/images/mine/real-action-img.png" mode="widthFix" />
    <text>为确保您能正常使用平台，请先进行实名认证</text>
    <wd-button @click="handleRealAction">
      实名链接推送
    </wd-button>
  </view>
</template>

<style scoped lang="scss">
.no-real-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;

  image {
    width: 280rpx;
  }

  text {
    color: #0c1433;
    font-size: 26rpx;
  }

  :deep(.wd-button) {
    margin-top: 60rpx;
    width: 300rpx;
    font-size: 26rpx;
    height: 85rpx !important;
  }
}
</style>
