<script setup lang="ts">
import { getRoleDeptInfos } from '@/api/login'
import { useUserStore } from '@/store'

interface Props {
  modelValue: string
}

interface Emits {
  (e: 'update:modelValue', roleId: string): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
})
const emit = defineEmits<Emits>()
// 选择的部门ID
const selectedRole = ref('')
// 当前角色
const currentUserRoleId = ref('')
// 获取userName
const userName = useUserStore().userInfo.userName
// 当前角色标记
const hasCurrenUser = ref(false)
// 部门列表
const roles = ref([])

// 获取部门列表
async function getRoleList() {
  try {
    const res = await getRoleDeptInfos(userName)
    roles.value = res.object
    roles.value = res.object.map(role => ({ ...role, description: role.detailsList.map(roles => roles.roleName).join('、') }))
  }
  catch (error) {
    console.error(error)
  }
}

// 处理角色选择
function handleRoleSelect(deptId: string) {
  selectedRole.value = deptId
  emit('update:modelValue', deptId)
}

watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    if (!currentUserRoleId.value && !hasCurrenUser.value) {
      currentUserRoleId.value = newValue
    }
  }
  hasCurrenUser.value = true // 确保immediate执行后判断是否有当前角色
  selectedRole.value = newValue
}, { immediate: true })

onMounted(async () => {
  await getRoleList()
})
</script>

<template>
  <view class="role-scroll-container">
    <view class="role-list">
      <view
        v-for="role in roles" :key="role.id" class="role-item"
        :class="{
          'role-item--selected': selectedRole === role.deptId || (!selectedRole && currentUserRoleId === role.deptId),
          'role-item--current': currentUserRoleId === role.deptId,
        }"
        @click="handleRoleSelect(role.deptId)"
      >
        <view class="role-content">
          <view class="role-header">
            <view class="role-name">
              {{ role.deptName }}
            </view>
          </view>
          <text class="role-description">
            {{ role.description }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.role-scroll-container {
  height: 100%;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

.role-list {
  min-height: 100%;
}

.role-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 24rpx;
  margin-bottom: 30rpx;
  border: 1rpx solid $uni-color-primary-light-6;
  border-radius: 22rpx;
  transition: all 0.3s ease;
  position: relative;

  &:last-child {
    margin-bottom: 0;
  }

  &::after {
    content: '';
    position: absolute;
    top: -5rpx;
    right: -5rpx;
    width: 90rpx;
    height: 65rpx;
    background-image: url('@/static/images/mine/card-tag-success.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    z-index: 1;
  }

  &--selected {
    background: #eaf6f2;
    border-color: $uni-color-primary;

    &::after {
      background-image: url('@/static/images/mine/card-tag-success-active.png');
    }
  }

  &--current {
    &::after {
      content: '当前角色';
      width: 140rpx;
      background-image: url('@/static/images/mine/card-tag-active.png');
      color: #fff;
      font-size: 26rpx;
      display: flex;
      justify-content: flex-end;
      align-items: flex-start;
      padding-right: 20rpx;
      padding-top: 10rpx;
      z-index: 3;
    }
  }
}

.role-content {
  flex: 1;
  margin-right: 24rpx;

  .role-header {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;

    .role-name {
      font-size: 28rpx;
      font-weight: 600;
      color: #0c1433;
      margin-right: 16rpx;
      position: relative;
      z-index: 3;

      &::after {
        content: '';
        z-index: -1;
        position: absolute;
        bottom: 0;
        width: 100%;
        left: 0;
        height: 16rpx;
        background: $uni-color-primary;
        filter: blur(1px);
        opacity: 0.4;
      }
    }

    .role-tag {
      padding: 4rpx 12rpx;
      background: #ff9500;
      color: #fff;
      font-size: 20rpx;
      border-radius: 8rpx;
      white-space: nowrap;
    }
  }

  .role-description {
    font-size: 25rpx;
    color: #38383a;
    line-height: 1.6;
    word-break: break-all;
  }
}

.role-scroll-container {
  &::-webkit-scrollbar {
    width: 6rpx;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3rpx;
  }

  &::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 3rpx;

    &:hover {
      background: transparent;
    }
  }
}
</style>
