import { VueQueryPlugin } from '@tanstack/vue-query'
import { createSSRApp } from 'vue'
import { selectDictLabel } from '@/utils/common'
import { useDict } from '@/utils/dict'
import App from './App.vue'
import { prototypeInterceptor, requestInterceptor, routeInterceptor } from './interceptors'

import store from './store'
import '@/style/index.scss'
import 'virtual:uno.css'

uni.$zp = {
  config: {
    'loading-more-title-custom-style': {
      'font-size': '26rpx',
    },
    'refresher-title-style': {
      'font-size': '26rpx',
    },
    'default-page-size': 20,
    'hide-no-more-inside': true,
    'paging-style': {
      'padding-bottom': 'env(safe-area-inset-bottom)',
      'box-sizing': 'border-box',
    },
  },
}

export function createApp() {
  const app = createSSRApp(App)
  app.use(store)
  app.use(routeInterceptor)
  app.use(requestInterceptor)
  app.use(prototypeInterceptor)
  app.use(VueQueryPlugin)
  app.config.globalProperties.useDict = useDict
  app.config.globalProperties.selectDictLabel = selectDictLabel
  return {
    app,
  }
}
