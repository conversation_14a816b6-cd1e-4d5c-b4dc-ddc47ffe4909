<template>
    <wd-col-picker v-bind="$attrs" v-model="internalValue" :columns="processedColumns"
        :display-format="displayFormat" :column-change="handleColumnChange" auto-complete />
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { ModeType } from './types'

// 只定义必要的props
const props = defineProps({
    mode: {
        type: String as PropType<ModeType>,
        default: 'cascader'
    },
    modelValue: {
        type: Array,
        default: () => []
    },
    columns: {
        type: Array,
        default: () => []
    }
})

// 定义事件
const emit = defineEmits([
    'update:modelValue'
])

// 内部值管理
const internalValue = ref<any[]>(props.modelValue || [])

// 处理columns数据，根据mode进行不同的处理
const processedColumns = computed(() => {
    if (!props.columns) return []

    if (props.mode === 'cascader') {
        //对数据进行回显处理
        if (Array.isArray(internalValue.value) && internalValue.value.length > 0) {
            let _columns = [props.columns],
                parentColumns = props.columns
            //分别根据值，从每一列中找到该值对应的子列，第一个值对应props.columns,后续的值从子列中找到
            internalValue.value.forEach((item: any) => {
                const children = getChildrenColumns(parentColumns, item)
                parentColumns = children
                if (children.length > 0) {
                    _columns = [..._columns, children]
                }
            })
            return _columns;
        }
        // 级联模式：如果传入的是单层数组，包装成多层结构
        return Array.isArray(props.columns[0]) ? props.columns : [props.columns]
    } else {
        // 树形模式：直接返回
        return props.columns
    }
})

const getChildrenColumns = (columns: any[], selectedItem: any) => {
    return columns.find((col: any) => col.value === selectedItem)?.children || []
}

// 监听外部值变化
watch(
    () => props.modelValue,
    (newVal) => {
        if (JSON.stringify(internalValue.value) !== JSON.stringify(newVal)) {
            internalValue.value = newVal
        }
    },
    { deep: true }
)

// 监听内部值变化，同步到外部
watch(
    () => internalValue.value,
    (newVal) => {
        emit('update:modelValue', newVal)
    },
    { deep: true }
)

const handleColumnChange = ({ selectedItem, resolve, finish }: { selectedItem: any, resolve: any, finish: any }) => {
    if (selectedItem.children && selectedItem.children.length > 0) {
        resolve(selectedItem.children)
    } else {
        finish()
    }
}

const displayFormat = (selectedItems: Record<string, any>[]) => {
    return selectedItems.map((item: any) => item.label || item.value).join('/')
}

// 暴露方法供父组件调用
defineExpose({
    // 获取当前值
    getValue: () => internalValue.value,
    // 设置值
    setValue: (value: any) => {
        internalValue.value = value
    },
    // 重置
    reset: () => {
        internalValue.value = Array.isArray(props.modelValue) ? [] : []
    }
})
</script>

<style lang="scss" scoped>
// 如果需要自定义样式，可以在这里添加</style>