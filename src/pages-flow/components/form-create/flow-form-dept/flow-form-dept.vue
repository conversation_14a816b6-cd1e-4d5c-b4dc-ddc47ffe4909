<script setup lang="ts">
import { ref } from 'vue'
import { getDeptTreeAPI } from '@/api/system/dept'

const props = defineProps({
  labelWidth: {
    type: String,
    default: '33%',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '请选择部门',
  },
  modelValue: {
    type: [String, Number],
    default: '',
  },
})

const columns = ref([])

// 初始化第一列数据
async function initDeptData() {
  try {
    const res = await getDeptTreeAPI()
    columns.value = res.data // 第一列数据
  }
  catch (error) {
    console.error('获取部门数据失败:', error)
  }
}

// 递归查找选中项的完整标签路径
function getSelectedLabels() {
  if (!props.modelValue || !columns.value.length)
    return ''

  // 递归查找函数
  const findLabel = (tree) => {
    for (const item of tree) {
      if (item.id === props.modelValue) {
        return item.label
      }
      if (item.children) {
        const found = findLabel(item.children)
        if (found) {
          return `${item.label}-${found}`
        }
      }
    }
    return null
  }

  const result = findLabel(columns.value)
  return result || ''
}

initDeptData()
</script>

<template>
  <view class="form-dept-picker">
    <wd-cell :title="title" :value="getSelectedLabels()" :title-width="labelWidth" ellipsis />
  </view>
</template>

<style scoped lang="scss"></style>
