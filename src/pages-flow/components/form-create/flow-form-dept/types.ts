import { baseProps, makeBooleanProp, makeStringProp } from 'wot-design-uni/components/common/props'


export const medicalProps = {
    ...baseProps,
    /**
     * 绑定值
     */
    modelValue: {
        type: Object,
        default: () => ({})
    },
    /**
     * 表单字段标识符，用于表单验证
     */
    prop: makeStringProp(''),
    /**
     * 输入框占位符
     */
    placeholder: makeStringProp('请输入发生年份'),
    /**
   * 是否必填
   */
    required: makeBooleanProp(false),
    /**
     * 表格标题
     */
    title: makeStringProp(''),
    /**
     * 是否禁用
     */
    disabled: makeBooleanProp(false),
}

export const elements = ['DM', 'CER', 'CH', 'CHD', 'CHR', 'GOUT', 'HBP', 'HLP', 'ICH']