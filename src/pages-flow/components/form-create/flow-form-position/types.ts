import { baseProps, makeBooleanProp, makeArrayProp, makeStringProp, numericProp } from 'wot-design-uni/components/common/props'


export const positionProps = {
    ...baseProps,
    /**
     * 绑定值
     */
    modelValue: makeArrayProp<number>(),
    /**
     * 表单字段标识符，用于表单验证
     */
    prop: makeStringProp(''),
    /**
     * 是否禁用
     */
    disabled: makeBooleanProp(false),
    /**
     * 输入框占位符
     */
    placeholder: makeStringProp(''),
    /**
     * 是否允许点击地图
     */
    allowMapClick: makeBooleanProp(true),
    /**
     * 是否允许搜索
     */
    allowSearch: makeBooleanProp(true)
}
