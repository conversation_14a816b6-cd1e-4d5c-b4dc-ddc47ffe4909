<template>
    <wd-col-picker v-bind="$attrs" v-model="internalValue" :columns="columns" :column-change="columnChange"
        @confirm="handleConfirm" :display-format="displayFormat" auto-complete></wd-col-picker>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import { useColPickerData, useRegionTranslate } from './regionPickerData'
const { colPickerData, findChildrenByCode } = useColPickerData()
import type { ModeType } from './types'

// 定义事件
const emit = defineEmits([
    'update:modelValue'
])
const columns = ref<any[]>([
    colPickerData.map((item) => {
        return {
            value: item.value,
            label: item.text
        }
    })
])
// 只定义必要的props
const props = defineProps({
    mode: {
        type: String as PropType<ModeType>,
        default: 'addr'
    },
    modelValue: {
        type: Array as PropType<string[]>,
        default: () => []
    }
})
const internalValue = ref<string[]>(props.modelValue || [])
// 监听内部值变化，同步到外部
watch(
    () => internalValue.value,
    (newVal) => {
        emit('update:modelValue', props.mode === 'code' ?  newVal : useRegionTranslate(newVal))
    },
    { deep: true }
)

const columnChange = ({ selectedItem, resolve, finish }: { selectedItem: any, resolve: any, finish: any }) => {
    const areaData = findChildrenByCode(colPickerData, selectedItem.value)

    if (areaData && areaData.length) {
        resolve(
            areaData.map((item) => {
                return {
                    value: item.value,
                    label: item.text
                }
            })
        )
    } else {
        finish()
    }
}
const displayFormat = (selectedItems: Record<string, any>[]) => {
    return selectedItems.map((item: any) => item.label || item.value).join('/')
}
function handleConfirm({ value }: { value: any }) {
    console.log(value)
}
</script>