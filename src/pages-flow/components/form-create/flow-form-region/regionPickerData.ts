// 可以将此代码放置于项目src/hooks/useColPickerData.ts中
import { useCascaderAreaData } from '@vant/area-data'

export type CascaderOption = {
    text: string
    value: string
    children?: CascaderOption[]
}

/**
 * 使用'@vant/area-data'作为数据源，构造ColPicker组件的数据
 * @returns
 */
export function useColPickerData() {
    // '@vant/area-data' 数据源
    const colPickerData: CascaderOption[] = useCascaderAreaData()

    // 根据code查找子节点，不传code则返回所有节点
    function findChildrenByCode(data: CascaderOption[], code?: string): CascaderOption[] | null {
        if (!code) {
            return data
        }
        for (const item of data) {
            if (item.value === code) {
                return item.children || null
            }
            if (item.children) {
                const childrenResult = findChildrenByCode(item.children, code)
                if (childrenResult) {
                    return childrenResult
                }
            }
        }
        return null
    }

    return { colPickerData, findChildrenByCode }
}

/**
 * 将地区编码转换为对应的中文名，如 ['110000', '110100', '110101'] 转换为 ['北京市', '北京市', '东城区']
 * @param codes 地区编码数组
 * @returns 地区中文名数组
 */
export function useRegionTranslate(codes: string[]): string[] {
    const areaData = useCascaderAreaData()

    // 构建一个扁平化的编码到文本的映射，提高查找效率
    const codeTextMap = new Map<string, string>()

    function flattenAreaData(data: CascaderOption[]) {
        data.forEach(item => {
            codeTextMap.set(item.value, item.text)
            if (item.children) {
                flattenAreaData(item.children)
            }
        })
    }

    // 只在第一次调用时初始化映射
    if (codeTextMap.size === 0) {
        flattenAreaData(areaData)
    }

    // 转换编码为文本
    return codes.map(code => codeTextMap.get(code) || code)
}