

export interface FormOption {
    label: string;
    value: any;
    [key: string]: any;
}

export interface WotSelectOption {
    label: string;
    value: any;
}

export interface FormFieldProps {
    placeholder?: string;
    type?: string;
    checkedMax?: number;
    action?: string;
    headers?: Record<string, any>;
    maxCount?: number;
    onSuccess?: string;
    defaultValue?: any;
    clearable?: boolean;
    [key: string]: any;
}

export interface FormField {
    _fc_id: string;
    type: string;
    field: string;
    title: string;
    props?: FormFieldProps;
    options?: FormOption[];
    $required?: boolean | string;
    hidden?: boolean;
    display?: boolean;
    info?: string;
    name?: string;
    _fc_drag_tag?: string;
    effect?: { fetch?: string };
    // 子表单/表格表单的子规则
    children?: string[];
    columns?: FormField[][]; // For tableForm, array of columns, each column is an array of fields
    [key: string]: any;
}

export interface FormConfigForm {
    labelWidth?: number | string;
    labelAlign?: 'left' | 'right' | 'center';
    size?: 'small' | 'medium' | 'large';
    colon?: boolean;
    layout?: 'horizontal' | 'vertical';
    labelCol?: Record<string, any>;
    wrapperCol?: Record<string, any>;
    description?: string;
    errorType?: 'toast' | 'message' | 'none';
    cellGroupBorder?: boolean;
}

export interface FormConfig {
    form?: FormConfigForm;
    formData?: Record<string, any>;
    formName?: string;
    resetBtn?: { show?: boolean; innerText?: string };
    submitBtn?: { show?: boolean; innerText?: string };
}

// For component registration
export interface CustomFormComponentProps {
    modelValue: any; // For v-model
    field: FormField; // The field definition
    disabled?: boolean;
    // any other common props your custom components might need
} 