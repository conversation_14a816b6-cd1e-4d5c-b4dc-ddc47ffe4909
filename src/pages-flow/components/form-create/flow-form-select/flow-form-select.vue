<!-- flow-form-select.vue -->
<script setup lang="ts">
import FlowFormSealList from '@/pages-flow/components/form-create/flow-form-seal-list/index.vue'
import { http } from '@/utils/http'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})
const props = defineProps<{
  field: SelectField
  modelValue: string | number | Array<string | number>
  disabled?: boolean
  labelWidth?: string
  size?: string
  enableStyle?: boolean
  labelAlign?: string
  formData?: Record<string, any>
}>()

const emit = defineEmits(['update:modelValue', 'update:formData'])

interface SelectField {
  type: string
  field: string
  title: string
  options?: Array<{ label: string, value: string | number }>
  props?: Record<string, any>
  effect?: {
    fetch?: {
      action: string
      method?: string
      parse?: string
      query?: Record<string, any>
      data?: Record<string, any>
    }
  }
  $required?: boolean
}

const selectOptions = ref<Array<{ label: string, value: string | number }>>([])
const loading = ref(false)

// 判断是否需要从远程获取数据
const hasFetchEffect = computed(() => {
  return props.field.effect?.fetch?.action
})

// 获取选项数据
async function fetchOptions() {
  console.log('fetchOptions 被调用，fetchConfig:', props.field.effect?.fetch)
  if (!hasFetchEffect.value)
    return

  const fetchConfig = props.field.effect!.fetch!
  loading.value = true

  try {
    const res = await http({
      url: fetchConfig.action,
      method: fetchConfig.method || 'GET',
      params: fetchConfig.query || {},
      data: fetchConfig.data || {},
    })
    const resData = res.object || res.data
    // 处理返回数据
    if (fetchConfig.parse) {
      // 解析自定义函数字符串
      const parseStr = fetchConfig.parse
        .replace('[[FORM-CREATE-PREFIX-', '')
        .replace('-FORM-CREATE-SUFFIX]]', '')

      // 清理字符串中的换行符和多余空格
      const cleanedParseStr = parseStr.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim()
      try {
        // 创建解析函数
        // 使用更安全的方式创建函数
        const parseFunctionBody = cleanedParseStr.substring(
          cleanedParseStr.indexOf('{') + 1,
          cleanedParseStr.lastIndexOf('}'),
        )
        const parseFunction = new Function('res', 'rule', 'api', parseFunctionBody)
        const parsedData = parseFunction(resData, null, null)
        selectOptions.value = parsedData
        console.log('parsedData', parsedData)
        console.log('设置 selectOptions.value 后:', selectOptions.value)
      }
      catch (parseError) {
        // 如果解析失败，使用默认映射
        selectOptions.value = resData.map((item: any) => ({
          label: item.label || item.dictLabel || item.name || item.title || String(item),
          value: item.value || item.dictValue || item.id || item.key || String(item),
        }))
        console.log('默认映射后 selectOptions.value:', selectOptions.value)
      }
    }
    else {
      selectOptions.value = resData
      console.log('直接设置 selectOptions.value:', selectOptions.value)
    }
  }
  catch (error) {
    console.error('获取选项数据失败:', error)
    // 错误情况下设置空数组
    selectOptions.value = []
  }
  finally {
    loading.value = false
  }
}

// 计算实际使用的选项
const computedOptions = computed(() => {
  console.log('computedOptions 重新计算:', {
    hasFetchEffect: hasFetchEffect.value,
    selectOptionsLength: selectOptions.value.length,
    selectOptions: selectOptions.value,
    fieldOptions: props.field.options,
  })
  if (hasFetchEffect.value) {
    return selectOptions.value
  }
  return props.field.options || []
})

// 组件类型
const pickerType = computed(() => {
  if (props.field.type === 'checkbox') {
    return 'checkbox'
  }
  return 'radio'
})

// 监听 effect 变化
watch(
  () => props.field.effect,
  () => {
    if (hasFetchEffect.value) {
      fetchOptions()
    }
  },
  { immediate: true },
)

// 监听 options 变化
watch(
  () => props.field.options,
  (newOptions) => {
    if (!hasFetchEffect.value && newOptions) {
      selectOptions.value = newOptions
    }
  },
  { immediate: true },
)

// 印章列表选择
const showSealSelect = ref(false)
function toSelectSeal() {
  showSealSelect.value = true
}

function onConfirmSeal(seal) {
  emit('update:modelValue', seal.id)
  const obj = {
    ...props.formData,
    sealId: seal.id,
    sealIdShow: seal.id,
    sealNo: seal.sealNo,
    sealTypeName: seal.sealTypeName,
    sealDeptName: seal.deptName,
    sealDeptId: seal.deptId,
  }
  emit('update:formData', obj)
  console.log('Selected Seal:', seal, props.formData)
}
</script>

<template>
  <view>
    <!-- 印章特定样式 -->
    <wd-cell
      v-if="field.field === 'sealId'"
      vertical
      class="select-seal"
      :prop="field.field"
    >
      <template #title>
        <view class="select-seal-title">
          {{ field.title }}
        </view>
      </template>
      <view class="select-seal-content" @click="toSelectSeal">
        <view class="name">
          请选择印章
        </view>
        <view class="text-primary">
          去选择<wd-icon name="arrow-right" size="22rpx" />
        </view>
      </view>
    </wd-cell>
    <wd-select-picker
      v-else
      :model-value="modelValue"
      :prop="field.field"
      :title="field.title"
      :label="field.title"
      align-right
      :size="size"
      :label-width="labelWidth"
      :show-confirm="false"
      :disabled="disabled"
      :required="!!field.$required"
      :placeholder="field.disabled ? '无' : field.props?.placeholder"
      :ellipsis="true"
      :type="pickerType"
      :max="field.props?.max || 0"
      :min="field.props?.min || 0"
      :columns="computedOptions"
      :loading="loading"
      :custom-label-class="
        enableStyle && labelAlign === 'right' ? 'form-label-class' : ''
      "
      :z-index="111"
      @change="(val: any) => emit('update:modelValue', val.value)"
    />

    <FlowFormSealList v-model="showSealSelect" @confirm="onConfirmSeal" />
  </view>
</template>

<style lang="scss" scoped>
.select-seal {
  :deep(.wd-cell__left.is-required) {
    padding-left: 0;
    &::after {
      display: none;
    }
  }
  :deep(.wd-cell__right) {
    margin-top: 10rpx !important;
  }
  .select-seal-title {
    font-size: 26rpx;
    @apply dot before:(dot-12-#F94E4F);
  }
  .select-seal-content {
    font-size: 24rpx;
    background: #f7f8f9;
    border: 1rpx solid #e0e0e0;
    color: #aaaaaa;
    @apply flex-x-between p-x-20rpx rounded-10rpx h-70rpx;
  }
}
</style>
