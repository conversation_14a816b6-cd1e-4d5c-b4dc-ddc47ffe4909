# flow-form-table 移动端表格表单组件

## 概述

`flow-form-table` 是一个专为移动端设计的表格表单组件，基于 `wot-design-uni` 组件库开发。该组件采用纵向卡片式布局，支持动态表单字段渲染、行的添加删除、表单验证等功能。

## 特性

- ✅ **移动端优化**：纵向卡片式布局，适合移动端操作
- ✅ **动态表单渲染**：支持多种表单字段类型
- ✅ **折叠展开**：支持行内容的折叠和展开
- ✅ **添加删除行**：支持动态添加和删除表格行
- ✅ **表单验证**：集成完整的表单验证功能
- ✅ **数据双向绑定**：支持 v-model 双向数据绑定
- ✅ **最大行数限制**：支持设置最大行数限制
- ✅ **禁用状态**：支持整体禁用功能
- ✅ **验证错误状态显示**：支持错误状态显示（红色边框、错误信息）

## 安装

该组件是 `flow-form-rendering` 插件的一部分，确保已安装相关依赖：

```bash
# 确保已安装 wot-design-uni
npm install wot-design-uni
```

## 基础用法

```vue
<template>
  <flow-form-table
    v-model="tableData"
    title="用户信息表"
    :columns="columns"
    :max-rows="10"
    :disabled="false"
  />
</template>

<script setup>
import { ref } from 'vue'

const tableData = ref([])

const columns = ref([
  {
    label: '基本信息',
    required: 'true',
    rule: [
      {
        _fc_id: 'name_field',
        type: 'input',
        field: 'name',
        title: '姓名',
        $required: true,
        props: {
          placeholder: '请输入姓名',
          allowClear: true
        }
      },
      {
        _fc_id: 'age_field',
        type: 'inputNumber',
        field: 'age',
        title: '年龄',
        $required: true,
        props: {
          min: 0,
          max: 120,
          placeholder: '请输入年龄'
        }
      }
    ]
  }
])
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| modelValue | 表格数据，支持 v-model | `Array<Record<string, any>>` | `[]` |
| title | 表格标题 | `string` | `''` |
| columns | 表格列配置 | `Array<ITableColumn>` | `[]` |
| maxRows | 最大行数，0表示不限制 | `number` | `0` |
| disabled | 是否禁用 | `boolean` | `false` |

## ITableColumn 类型定义

```typescript
interface ITableColumn {
  label: string        // 列标题
  required: string     // 是否必填（字符串形式）
  style?: Object       // 样式配置
  rule?: Array<FormField>  // 表单字段规则数组
}
```

## FormField 支持的字段类型

### 1. 输入框 (input)

```javascript
{
  type: 'input',
  field: 'fieldName',
  title: '字段标题',
  $required: true,
  props: {
    type: 'text', // text, number, password, textarea
    placeholder: '请输入内容',
    maxLength: 100,
    allowClear: true
  }
}
```

### 2. 数字输入 (inputNumber)

```javascript
{
  type: 'inputNumber',
  field: 'fieldName',
  title: '数字字段',
  props: {
    min: 0,
    max: 100,
    step: 1,
    precision: 0
  }
}
```

### 3. 选择器 (radio/checkbox/select)

```javascript
{
  type: 'radio', // 或 'checkbox', 'select'
  field: 'fieldName',
  title: '选择字段',
  options: [
    { label: '选项1', value: 'value1' },
    { label: '选项2', value: 'value2' }
  ],
  props: {
    max: 3, // checkbox 最大选择数量
    min: 1  // checkbox 最小选择数量
  }
}
```

### 4. 开关 (switch)

```javascript
{
  type: 'switch',
  field: 'fieldName',
  title: '开关字段',
  props: {
    checkedValue: true,
    unCheckedValue: false
  }
}
```

### 5. 评分 (rate)

```javascript
{
  type: 'rate',
  field: 'fieldName',
  title: '评分字段',
  props: {
    count: 5,
    allowHalf: true
  }
}
```

### 6. 滑块 (slider)

```javascript
{
  type: 'slider',
  field: 'fieldName',
  title: '滑块字段',
  props: {
    min: 0,
    max: 100,
    step: 1
  }
}
```

### 7. 日期时间选择 (datePicker/timePicker)

```javascript
{
  type: 'datePicker', // 或 'timePicker'
  field: 'fieldName',
  title: '日期字段',
  props: {
    picker: 'date' // date, time, datetime, year-month
  }
}
```

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| update:modelValue | 数据更新时触发 | `(value: Array<Record<string, any>>) => void` |

## Methods

通过 ref 可以调用以下方法：

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|--------|
| addRow | 添加新行 | - | - |
| deleteRow | 删除指定行 | `(index: number) => void` | - |
| validate | 验证表单 | - | `Promise<{valid: boolean, errors: any[]}>` |
| resetForm | 重置表单 | - | - |
| getData | 获取表格数据 | - | `Array<Record<string, any>>` |
| setData | 设置表格数据 | `(data: Array<Record<string, any>>) => void` | - |

## 使用示例

### 完整示例

```vue
<template>
  <view class="page">
    <flow-form-table
      ref="tableFormRef"
      v-model="formData"
      title="员工信息登记表"
      :columns="tableColumns"
      :max-rows="20"
      :disabled="isDisabled"
    />
    
    <view class="actions">
      <wd-button @click="validateForm">验证表单</wd-button>
      <wd-button @click="resetForm">重置表单</wd-button>
      <wd-button @click="submitForm">提交表单</wd-button>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

const tableFormRef = ref()
const formData = ref([])
const isDisabled = ref(false)

const tableColumns = ref([
  {
    label: '基本信息',
    required: 'true',
    rule: [
      {
        _fc_id: 'name',
        type: 'input',
        field: 'name',
        title: '姓名',
        $required: '姓名不能为空',
        props: {
          placeholder: '请输入姓名',
          allowClear: true
        }
      },
      {
        _fc_id: 'gender',
        type: 'radio',
        field: 'gender',
        title: '性别',
        $required: true,
        options: [
          { label: '男', value: 'male' },
          { label: '女', value: 'female' }
        ]
      }
    ]
  },
  {
    label: '工作信息',
    required: 'false',
    rule: [
      {
        _fc_id: 'department',
        type: 'select',
        field: 'department',
        title: '部门',
        options: [
          { label: '技术部', value: 'tech' },
          { label: '销售部', value: 'sales' },
          { label: '人事部', value: 'hr' }
        ]
      },
      {
        _fc_id: 'salary',
        type: 'inputNumber',
        field: 'salary',
        title: '薪资',
        props: {
          min: 0,
          max: 100000,
          step: 1000
        }
      }
    ]
  }
])

// 验证表单
const validateForm = async () => {
  try {
    const result = await tableFormRef.value?.validate()
    if (result.valid) {
      uni.showToast({ title: '验证通过', icon: 'success' })
    } else {
      uni.showToast({ title: '验证失败', icon: 'error' })
      console.log('验证错误：', result.errors)
    }
  } catch (error) {
    console.error('验证异常：', error)
  }
}

// 重置表单
const resetForm = () => {
  tableFormRef.value?.resetForm()
}

// 提交表单
const submitForm = async () => {
  const result = await validateForm()
  if (result?.valid) {
    const data = tableFormRef.value?.getData()
    console.log('提交数据：', data)
    // 执行提交逻辑
  }
}
</script>
```

## 样式定制

组件提供了丰富的 CSS 变量和深度选择器支持样式定制：

```scss
.flow-form-table {
  // 自定义卡片样式
  :deep(.table-row-card) {
    border-radius: 16rpx;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }
  
  // 自定义标题样式
  :deep(.column-title) {
    color: #1890ff;
    font-weight: bold;
  }
  
  // 自定义添加按钮样式
  :deep(.add-btn) {
    border-color: #52c41a;
    color: #52c41a;
  }
}
```

## 注意事项

1. **字段唯一性**：确保每个字段的 `field` 属性在同一行中是唯一的
2. **数据类型**：不同字段类型有不同的默认值和数据格式要求
3. **验证规则**：`$required` 可以是布尔值或自定义错误消息字符串
4. **性能优化**：大量数据时建议使用虚拟滚动或分页
5. **移动端适配**：组件已针对移动端优化，建议在移动端环境下使用

## 更新日志

### v1.0.0

- 初始版本发布
- 支持基础表格表单功能
- 支持多种表单字段类型
- 支持表单验证
- 支持移动端优化布局

## 错误状态显示

当表单验证失败时，组件会自动显示错误状态：

### 视觉反馈

1. **卡片边框**：变为红色边框，带有红色阴影
2. **标题颜色**：标题文字变为红色
3. **行卡片边框**：内部行卡片边框变为淡红色
4. **错误信息**：在组件底部显示具体的错误信息，带有警告图标

### 错误触发条件

1. **必填验证**：当设置 `required="true"` 且数据为空时
2. **字段验证**：当行内表单字段验证失败时
3. **自定义验证**：通过验证规则定义的其他验证条件

### 错误状态样式

```scss
// 卡片错误状态
:deep(.wd-card.is-error) {
    border-color: #ff4757;
    box-shadow: 0 0 0 2rpx rgba(255, 71, 87, 0.2);
}

// 错误信息显示
.error-message {
    color: #ff4757;
    background: rgba(255, 71, 87, 0.1);
    border: 1rpx solid rgba(255, 71, 87, 0.3);
    // 带有警告图标
}
```

## 测试错误状态

可以通过以下方式测试错误状态显示：

1. 设置 `required="true"`
2. 清空所有数据
3. 调用 `validate()` 方法
4. 组件会自动显示错误状态和错误信息
