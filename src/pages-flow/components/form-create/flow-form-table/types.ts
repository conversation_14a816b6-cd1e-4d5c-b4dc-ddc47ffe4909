import { baseProps, makeBooleanProp, makeArrayProp, makeStringProp, numericProp, makeNumberProp } from 'wot-design-uni/components/common/props'
import { FormField } from '../flow-form-rendering/types'

export interface ITableColumn {
    label: string
    required: string
    style?: Object
    rule?: Array<FormField>
}

export const tableProps = {
    ...baseProps,
    /**
     * 表单字段标识符，用于表单验证
     */
    prop: makeStringProp(''),
    /**
     * 表格列
     */
    columns: makeArrayProp<ITableColumn>(),
    /**
     * 最大行数，默认为0，表示不限制行数
     */
    maxRows: makeNumberProp<number>(0),
    /**
     * 绑定值
     */
    modelValue: makeArrayProp<Record<string, any>>(),
    /**
   * 是否必填
   */
    required: makeBooleanProp(false),
    /**
     * 表格标题
     */
    title: makeStringProp(''),
    /**
     * 是否禁用
     */
    disabled: makeBooleanProp(false),
}
