<template>
  <view class="form-item-placeholder"
    >{{ props.tips ?? "移动端不支持该组件 " }} {{ field }}</view
  >
</template>
<script setup lang="ts">
const props = defineProps({
  field: {
    type: String,
    required: true,
  },
  tips: String,
})
</script>
<style scoped lang="scss">
.form-item-placeholder {
  padding: 10rpx 20rpx;
  color: #999;
  border: 1px dashed #999;
  border-radius: 10rpx;
  margin: 20rpx 0;
}
</style>
