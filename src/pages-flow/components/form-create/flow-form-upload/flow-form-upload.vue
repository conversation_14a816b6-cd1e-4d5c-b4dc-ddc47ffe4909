<script lang="ts" setup name="FlowFormUpload">
import { imageApi } from '@/config/constant'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})

const props = defineProps({
  field: {
    type: Object,
    default: () => {},
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  modelValue: {
    type: [String, Array],
    default: '',
  },
  title: {
    type: String,
    default: '附件',
  },
})

const action = ref('')
const fileList = ref([])

watch(
  () => props.modelValue,
  (val) => {
    fileList.value = val.split(',').map((item) => {
      return {
        url: imageApi + item,
      }
    })
    console.log(fileList.value)
  },
  { immediate: true },
)
function handleChange(e: any) {
  console.log(e)
}
</script>

<template>
  <view class="flow-form-upload" :class="{ disabled }">
    <view class="flow-form-upload-title">
      {{ title }}
    </view>
    <wd-upload
      :file-list="fileList"
      image-mode="aspectFill"
      :action="action"
      :disabled="disabled"
      @change="handleChange"
    />
  </view>
</template>

<style lang="scss" scoped>
.flow-form-upload {
  padding: 0 30rpx;
  &.disabled {
    display: flex;
    .flow-form-upload-title {
      margin-right: 20rpx;
    }
  }
  &-title {
    color: #6b6b6b;
    margin-bottom: 20rpx;
  }
  :deep(.wd-upload__evoke.is-disabled) {
    display: none;
  }
}
</style>
