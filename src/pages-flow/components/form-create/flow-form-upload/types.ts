import { baseProps, makeArrayProp, makeStringProp, makeBooleanProp, makeNumberProp } from 'wot-design-uni/components/common/props'
import type { UploadFileType } from 'wot-design-uni/components/wd-upload/types'

export const uploadFormProps = {
    ...baseProps,
    /**
     * 绑定值 - 文件URL数组
     */
    modelValue: makeArrayProp<string>(),
    /**
     * 表单字段标识符，用于表单验证
     */
    prop: makeStringProp(''),
    /**
     * 上传地址
     */
    action: makeStringProp(''),
    /**
     * 服务端响应URL字段： { code: 200, data: 'http://localhost:82/yauee-db-form/dev/file/download?suffix=jpg&id=1927256465349828610' }
     */
    respUrlField: makeStringProp('data'),
    /**
     * 请求头
     */
    header: { type: Object, default: () => ({}) },
    /**
     * 最大上传数量
     */
    limit: Number,
    /**
     * 是否支持多选
     */
    multiple: makeBooleanProp(false),
    /**
     * 文件类型
     */
    accept: makeStringProp<UploadFileType>('all'),
    /**
     * 文件大小限制
     */
    maxSize: makeNumberProp(Number.MAX_VALUE),
    /**
     * 是否禁用
     */
    disabled: makeBooleanProp(false),
    /**
     * 表单额外数据
     */
    formData: { type: Object, default: () => ({}) },
    /**
     * 文件字段名
     */
    name: makeStringProp('file')
}
