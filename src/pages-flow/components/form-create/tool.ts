const tool: Record<string, any> = {}

/**
 * 判断值是否为空
 * - null
 * - undefined
 * - NaN
 * - 空字符串 (含全空格)
 * - 空数组
 * - 空对象 (不包含原型链上的属性)
 */
function isNullish(value: unknown): boolean {
    if (value === null || value === undefined) return true;
    if (Number.isNaN(value)) return true;
    if (typeof value === 'string' && value.trim() === '') return true;
    if (Array.isArray(value) && value.length === 0) return true;
    if (value instanceof Object && !Array.isArray(value) && Object.keys(value).length === 0) return true;
    return false;
}

/**
 * 判断值是否非空
 */
function isNotEmpty(value: unknown): boolean {
    return !isNullish(value);
}

/**
 * 如果值为空则返回默认值
 */
function nullable<T, U>(value: T, fallback: U): T | U {
    return isNullish(value) ? fallback : value;
}

function startWith(source: string, str: string) {
    return new RegExp("^" + str).test(source);
}

tool.string = {
    isNullish,
    isNotEmpty,
    nullable,
    startWith
}

export default tool;
