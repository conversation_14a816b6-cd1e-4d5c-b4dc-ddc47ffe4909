<script lang="ts" setup name="AuditPopup">
import { getConfigByConfigKeyAPI } from "@/api/system/index"
defineOptions({
  options: {
    styleIsolation: "shared",
  },
})

const show = ref(false)
const comment = ref(null) // 审批意见
const configKey = ref("agree") // 审批类型 agree:同意 reject:拒绝
const agreeList = ref([])
function open(type = "agree") {
  show.value = true
  configKey.value = type
  getConfigValue()
}

function handleClose() {
  show.value = false
  comment.value = null
}

async function getConfigValue() {
  try {
    const key = configKey.value === "agree" ? "flowCommentAgree" : "flowCommentReject"
    const res = await getConfigByConfigKeyAPI(key)
    agreeList.value = res.object.split(",") || []
  } catch (error) {}
}

function handleClick(item) {
  comment.value = item
}

function handleSubmit() {
  emit("submit", comment.value)
  handleClose()
}

getConfigValue()

defineExpose({
  open,
})
</script>

<template>
  <wd-popup
    v-model="show"
    closable
    position="bottom"
    :z-index="99"
    class="audit-popup"
    @close="handleClose"
  >
    <view class="audit-form">
      <view class="audit-form-title"> 审批意见 </view>
      <wd-textarea
        v-model="comment"
        placeholder="请填写审批意见"
        :maxlength="500"
        show-word-limit
        class="audit-form-textarea"
      />
      <view class="audit-form-help" v-if="agreeList.length">
        <view class="audit-form-help-text"> 快捷填入 </view>
        <view class="prompt-list">
          <view class="prompt-list-item" v-for="item in agreeList" :key="item" @click="handleClick(item)">
            {{ item }}
          </view>
        </view>
      </view>
      <view class="handle-btn">
        <wd-button type="info" plain @click="handleClose"> 取消 </wd-button>
        <wd-button @click="handleSubmit">确认</wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<style lang="scss" scoped>
.audit-popup {
  :deep(.wd-popup) {
    border-radius: 20rpx 20rpx 0 0;
    .wd-popup__close {
      color: #a9a9af;
      font-size: 30rpx;
      padding: 5rpx;
    }
  }
}

.audit-form {
  min-height: 1000rpx;
  padding: 30rpx;
  position: relative;
  overflow: hidden;
  .audit-form-title {
    font-size: 34rpx;
    font-weight: 500;
    margin-bottom: 20rpx;
  }
  .audit-form-textarea {
    background: #f9f9f9;
    border-radius: 16rpx;
    border: 1rpx solid #e0e0e0;
    padding: 0;
    :deep(.wd-textarea__value) {
      background: transparent;
      padding-bottom: 60rpx;
      .wd-textarea__inner {
        height: 260rpx;
        padding: 20rpx 30rpx;
      }
      .wd-textarea__count {
        background: transparent;
        bottom: 10rpx;
        right: 20rpx;
      }
    }
  }

  .handle-btn {
    display: flex;
    gap: 20rpx;
    position: absolute;
    width: 92%;
    left: 0;
    right: 0;
    margin: 0 auto;
    padding-bottom: 40rpx;
    bottom: 0;
    :deep(.wd-button) {
      flex: 1;
      height: 90rpx;
    }
  }
  .audit-form-help-text {
    font-size: 26rpx;
    margin: 40rpx 0 20rpx;
  }
  .prompt-list {
    display: flex;
    flex-wrap: wrap;
    .prompt-list-item {
      background: #f8f8f8;
      padding: 14rpx 20rpx;
      border-radius: 100rpx;
      margin-right: 20rpx;
      min-width: calc(25% - 20rpx);
      box-sizing: border-box;
      text-align: center;
      margin-bottom: 20rpx;
      color: #6b6b6b;
      font-size: 26rpx;
    }
  }
}
</style>
