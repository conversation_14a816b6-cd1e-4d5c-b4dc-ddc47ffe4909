<script lang="ts" setup>
import { getFlowCommentAPI } from "@/api/ess/flow/todo"

defineOptions({
  options: {
    styleIsolation: "shared",
  },
})

const props = defineProps({
  flowInstId: {
    type: String,
    default: "",
  },
})
const flowList = ref([])
async function getFlowComment() {
  const res = await getFlowCommentAPI({
    flowInstId: props.flowInstId,
    page: 1,
    size: 100,
  })
  flowList.value = res.object.records.reverse()
}
getFlowComment()
</script>

<template>
  <view class="flow-steps">
    <wd-steps :active="flowList.length" vertical>
      <wd-step v-for="item in flowList" :key="item.id">
        <template #icon>
          <view class="step-icon">
            <image
              class="step-user-icon"
              src="@/static/images/icon/step-user.png"
              mode="aspectFit"
            />
            <image
              v-if="+item.status === 0"
              class="status"
              src="@/static/images/icon/step-progress.png"
              mode="aspectFit"
            />
            <image
              v-else-if="+item.status === 0 || item.handleTypeName?.includes('拒绝')"
              class="status"
              src="@/static/images/icon/step-error.png"
              mode="aspectFit"
            />
            <image
              v-else-if="+item.status !== 0"
              class="status"
              src="@/static/images/icon/step-finish.png"
              mode="aspectFit"
            />
          </view>
        </template>
        <template #title>
          <view class="step-create-user">
            <view class="step-create-user-name">
              {{ item.roleName || item.remark }}
            </view>
            <view class="step-create-user-time">
              {{ item.createTime }}
            </view>
          </view>
        </template>
        <template #description>
          <view class="step-description">
            <view
              class="job-name"
              :class="{
                'text-warning': +item.status === 0,
                'text-danger': item.handleTypeName?.includes('拒绝'),
              }"
            >
              {{ item.handleTypeName || item.jobName }}
            </view>
            <view v-if="item.handleComment" class="step-description-content">
              <text>审批意见：</text>
              <text
                :class="{
                  'text-danger': item.handleTypeName?.includes('拒绝'),
                }"
              >
                {{ item.handleComment }}
              </text>
            </view>
          </view>
        </template>
      </wd-step>
    </wd-steps>
  </view>
</template>

<style lang="scss" scoped>
.flow-steps {
  :deep(.wd-step__icon.is-icon) {
    width: 68rpx;
    height: 68rpx;
  }
  :deep(.wd-step__content) {
    margin-left: 90rpx !important;
  }
  .step-icon {
    position: relative;
    background: #fff;
    border-radius: 50%;
    width: 68rpx;
    height: 68rpx;
    padding: 2rpx;
    .step-user-icon {
      width: 100%;
      height: 100%;
    }
    .status {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 28rpx;
      height: 28rpx;
    }
  }

  .step-create-user {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #0c1433;
    &-time {
      flex-shrink: 0;
      color: #aaaaaa;
      font-weight: 400;
      font-size: 24rpx;
    }
  }
  .step-description {
    font-size: 26rpx;
    margin-bottom: 20rpx;
    .job-name {
      color: $uni-color-primary;
      &.text-warning {
        color: $uni-color-warning;
      }
      &.text-danger {
        color: $uni-color-error !important;
      }
    }
    &-content {
      border-radius: 10rpx;
      padding: 20rpx;
      background: #f7f8f9;
      margin-top: 10rpx;
      color: #38383a;
    }
  }
}
</style>
