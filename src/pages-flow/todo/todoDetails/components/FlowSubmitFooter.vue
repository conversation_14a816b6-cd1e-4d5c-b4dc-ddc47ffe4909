<script lang="ts" setup>
defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})
const emit = defineEmits(['change'])
function onClick(type) {
  emit('change', type)
}
</script>

<template>
  <view class="flow-submit-footer">
    <wd-button type="info" plain @click="onClick('reject')">
      退回
    </wd-button>
    <wd-button type="error" @click="onClick('reject')">
      拒绝
    </wd-button>
    <wd-button type="primary" @click="onClick('agree')">
      同意
    </wd-button>
  </view>
</template>

<style lang="scss" scoped>
.flow-submit-footer {
  display: flex;
  gap: 12rpx;
  width: 100%;
  background-color: #fff;
  padding: 20rpx 20rpx calc(20rpx + env(safe-area-inset-bottom));
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 66;
  :deep(.wd-button) {
    height: 80rpx;
    width: calc(33% - 20rpx);
  }
}
</style>
