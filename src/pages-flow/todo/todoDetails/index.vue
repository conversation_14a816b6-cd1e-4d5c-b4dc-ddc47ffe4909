<route lang="json5" type="page">
{
  layout: "default",
  needLogin: true,
  style: {
    navigationStyle: "custom",
    navigationBarTitleText: "详情",
  },
}
</route>

<script lang="ts" setup name="TodoDetails">
import { getFlowDetailInfoAPI, getFlowFormDataAPI, getFlowFormOptionsAPI } from '@/api/ess/flow/todo'
import SkeletonList from '@/components/skeleton-list/index.vue'
// import FlowFormRendering from '@/pages-flow/components/form-create/flow-form-rendering/flow-form-rendering.vue'
import FlowFormRendering from '../../components/form-create/flow-form-rendering/flow-form-rendering.vue'
import AuditPopup from './components/AuditPopup.vue'
import FlowSteps from './components/FlowSteps.vue'
import FlowSubmitFooter from './components/FlowSubmitFooter.vue'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})

const loading = ref(true)
const isHiJob = ref('1')
const form = ref({})
const curForm = ref({})
const formData = ref({}) // 表单数据
const formSettingData = ref({})
const nodeSettings = ref({}) // 节点设置
async function getData() {
  try {
    const { id, flowInstId } = form.value
    const res = await getFlowDetailInfoAPI({ id, isHiJob: 0, flowInstId })
    const formDataRes = await getFlowFormDataAPI(res.object.orderId)
    const formSettingRes = await getFlowFormOptionsAPI({
      type: 1,
      formType: 0,
      formId: formDataRes.object.formId,
      flowInstId: formDataRes.object.flowInstId,
      defFlowId: formDataRes.object.defFlowId,
      flowNodeId: res.object.flowNodeId,
    })
    console.log(res, formDataRes, formSettingRes)
    curForm.value = res.object
    form.value = formDataRes.object
    nodeSettings.value = formSettingRes.object
    formSettingData.value = res.object.elTabs[0]
  }
  finally {
    loading.value = false
  }
}

const auditPopupRef = ref()
const formRef = ref()
function handleShowAudit(type) {
  formRef.value.handleSubmit().then((res) => {
    auditPopupRef.value.open(type)
  })
}

onLoad((option) => {
  console.log(option)
  form.value = option
  isHiJob.value = option.isHiJob
  getData()
})
</script>

<template>
  <view class="app-container">
    <fg-navbar>详情</fg-navbar>
    <SkeletonList v-if="loading" col-height="50rpx" :row-count="10" />
    <view v-if="form.formInfo && curForm.order" class="page-main">
      <view class="apply-user-info">
        <view class="apply-user-info-title" :class="{ 'p-r-140rpx': isHiJob === '0' }">
          {{ curForm.order.createUserName }}({{
            curForm.order.createUserWorkNumber
          }})提交的{{ curForm.order.formName }}
        </view>
        <view v-if="isHiJob === '0'" class="details-tag">
          待我处理
        </view>
        <view class="user-dept">
          {{ curForm.order.createUserDeptName }}
        </view>
        <view class="apply-form">
          <view class="form-item">
            <span class="form-item-label">审批工号：</span>
            <span class="form-item-content">{{ curForm.order.code }}</span>
          </view>
          <view class="form-item">
            <span class="form-item-label">申请时间：</span>
            <span class="form-item-content">{{ curForm.order.createTime }}</span>
          </view>
        </view>
      </view>
      <view class="apply-form-base">
        <view class="box-title">
          申请基本信息
        </view>
        <flow-form-rendering
          ref="formRef"
          v-model="formData"
          :rules="form.formInfo"
          :form-data="form.formData"
          :options="form.formInfo"
          :form-base-info="curForm"
          :node-settings="nodeSettings"
          :disabled="+formSettingData.isFormEdit === 0"
        />
      </view>

      <view class="flow-step">
        <view class="box-title">
          流程
        </view>
        <FlowSteps :flow-inst-id="form.flowInstId" />
      </view>

      <FlowSubmitFooter class="handle-box" @change="handleShowAudit" />
    </view>

    <AuditPopup ref="auditPopupRef" />
  </view>
</template>

<style lang="scss" scoped>
.page-main {
  padding: 10rpx 30rpx 150rpx;
}

.white-round-box {
  background: #fff;
  border-radius: 18rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  position: relative;
}

.apply-user-info {
  @extend .white-round-box;
  .details-tag {
    position: absolute;
    right: 0;
    top: 25rpx;
    font-size: 24rpx;
    padding: 7rpx 18rpx 7rpx 20rpx;
    border-radius: 100rpx 0 0 100rpx;
    background: #ffefe3;
    color: #ff7912;
  }
  .apply-user-info-title {
    @apply title-line;
  }
  .user-dept {
    background: #f0f1f3;
    padding: 7rpx 20rpx;
    @apply inline-block text-center min-w-90rpx text-24rpx color-#6B6B6B rounded-50rpx m-y-20rpx;
  }
  .apply-form {
    font-size: 26rpx;
    .form-item {
      margin-bottom: 10rpx;
      .form-item-label {
        color: #6b6b6b;
        margin-right: 50rpx;
      }
    }
  }
}

.apply-form-base {
  @extend .white-round-box;
  padding: 20rpx 10rpx;
  .box-title {
    padding-left: 20rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
  }
}

.flow-step {
  @extend .white-round-box;
  .box-title {
    font-weight: bold;
    margin-bottom: 20rpx;
  }
}
</style>
