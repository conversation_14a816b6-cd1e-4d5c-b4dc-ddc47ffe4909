<script lang="ts" setup>
import { getSignAppLinkAPI } from '@/api/ess/flow/todo'

const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
  type: {
    type: [String, Number],
    default: 'flow', // flow流程---file文件
  },
  currentTab: {
    // 0待我处理 1进行中 2即将超时 3已完结
    type: [String, Number],
    default: 0,
  },
  showNode: {
    // 是否显示节点
    type: Boolean,
    default: false,
  },
  // 字典
  dictOptions: {
    type: Object,
    default: () => ({
      flow_status: [],
      flow_job_end: [], // 流程完结状态
    }),
  },
  // 展示文件状态
  showFileStatus: {
    type: Boolean,
    default: false,
  },
  // 展示停留时间
  showStayTime: {
    type: Boolean,
    default: true,
  },
  // 展示结束时间
  showEndTime: {
    type: Boolean,
    default: false,
  },
  // 是否待我处理
  isHiJob: {
    type: [String, Number],
    default: 1, // 0是待处理
  },
})

const { proxy } = getCurrentInstance()

// 计算dot颜色
function dotColor() {
  if (props.showFileStatus) {
    return getFlowStatusClass()
  }
  else {
    // 处理停留时间（staySeconds）逻辑
    if (props.type === 'flow' && props.item.staySeconds) {
      const stayDays = +props.item.staySeconds / 86400
      if (stayDays > 5)
        return 'dot-red'
      if (stayDays > 3)
        return 'dot-blue' // 3-5天显示蓝色
      return ''
    }

    // 处理剩余时间（remainTimeSeconds）逻辑
    if (props.type === 'file' && props.item.remainTimeSeconds) {
      const remainDays = +props.item.remainTimeSeconds / 86400
      if (remainDays < 1)
        return 'dot-red'
      if (remainDays < 3)
        return 'dot-blue' // 1-3天显示蓝色
      return ''
    }

    return ''
  }
}
// 计算剩余时间颜色
const timeColor = computed(() => {
  const seconds = +props.item.remainTimeSeconds || 0
  if (seconds < 86400) {
    // 仅剩一天
    return 'text-red'
  }
  return ''
})

function getFlowStatusClass() {
  const { dictOptions, type } = props
  const dictArr = type === 'file' ? dictOptions.flow_status : dictOptions.flow_job_end
  const dictKey = type === 'file' ? 'flowStatus' : 'status'
  if (!dictArr)
    return ''
  const obj = dictArr.find(i => i.value === props.item[dictKey])
  return obj?.elTagClass || ''
}
function getDictLabel() {
  const { dictOptions, type } = props
  const dictArr = type === 'file' ? dictOptions.flow_status : dictOptions.flow_job_end
  const dictKey = type === 'file' ? 'flowStatus' : 'status'
  if (!dictArr)
    return ''
  return proxy.selectDictLabel(dictArr, props.item[dictKey])
}

// 跳转详情
async function toDetails() {
  if (props.type === 'flow') {
    uni.navigateTo({
      url: `/pages-flow/todo/todoDetails/index?id=${props.item.id}&flowInstId=${props.item.flowInstId}&isHiJob=${props.isHiJob}`,
    })
  }
  else {
    if (!props.isHiJob)
      return
    try {
      uni.showLoading({ title: '' })
      const res = await getSignAppLinkAPI(props.item.essFlowId)
      let openMiniProgram = uni.navigateToMiniProgram
      if (uni.canIUse('openEmbeddedMiniProgram')) {
        openMiniProgram = uni.openEmbeddedMiniProgram
      }
      console.log(uni.canIUse('openEmbeddedMiniProgram'))
      openMiniProgram({
        appId: import.meta.env.VITE_TX_QIAN_APPID,
        path: res.object.url,
      })
    }
    finally {
      uni.hideLoading()
    }
  }
}
</script>

<template>
  <view class="todo-item" @click="toDetails">
    <view
      class="todo-item-header dot"
      :class="[dotColor(), { 'p-r-200rpx!': type === 'file' }]"
    >
      {{ item.flowName }}
    </view>
    <view v-if="showFileStatus" :class="getFlowStatusClass()" class="todo-item-status">
      {{ getDictLabel() }}
    </view>
    <view class="todo-item-content">
      <view class="todo-item-content-item">
        <text>{{ type === "flow" ? "申请人" : "发起人" }}</text>
        {{ item.createUserName || item.startUserName }}
        <template v-if="item.workNumber">
          ({{ item.workNumber || item.startUserWorkNumber }}-{{
            item.deptName || item.startDeptName
          }})
        </template>
      </view>
      <view class="todo-item-content-item">
        <text>开始时间</text>
        {{ item.startTime || item.createTime }}
      </view>
      <view v-if="showEndTime || type === 'file'" class="todo-item-content-item">
        <text>结束时间</text>
        {{ item.signLimitTime || item.endTime }}
      </view>

      <view v-if="type === 'file'" class="todo-item-content-item">
        <text>系统来源</text>
        {{ item.sourceName || "-" }}
      </view>
    </view>
    <view v-if="showStayTime" class="bottom-node" :class="{ 'flex-x-between': showNode }">
      <view v-if="showNode && type == 'flow'" class="node-box">
        <image src="@/static/images/icon/todo-user.png" mode="widthFix" />
        <view class="flex-x-center">
          <text class="inline-block max-w-300rpx truncate">
            {{ item.nodeName }}
          </text>
          <!-- 处理中 -->
        </view>
      </view>
      <text v-if="type === 'flow'">
        已停留 {{ item.stayTime }}
      </text>
      <text v-if="type === 'file'" class="time" :class="timeColor">
        剩余 {{ item.remainTime }}
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.todo-item {
  @apply bg-#fff rounded-18rpx mt-20rpx overflow-hidden relative;
  .todo-item-status {
    position: absolute;
    right: 0;
    top: 15rpx;
    padding: 7rpx 18rpx 7rpx 20rpx;
    border-radius: 100rpx 0 0 100rpx;
    line-height: 36rpx;
    min-width: 110rpx;
    text-align: center;
    font-size: 26rpx;
    &.text-warning,
    &.text-warning-light {
      background-color: #ffefe3;
      color: #ff7912;
    }
    &.text-blue {
      background-color: #dbecff;
      color: #2e89ff;
    }
    &.text-primary {
      background-color: #eafaf0;
      color: $uni-color-primary;
    }
    &.text-muted,
    &.text-info {
      background: #f2f3f3;
      color: #aaaaaa;
    }
    &.text-danger {
      background-color: #ffe9e9;
      color: #ff4d4f;
    }
  }
  .todo-item-header {
    @apply text-28rpx font-600 p-r-30rpx p-l-50rpx p-y-20rpx before:(left-30rpx!);
    background: linear-gradient(to right, $uni-color-primary-light-8, #fff);
    word-break: break-all;
    // dot 蓝色
    &.dot-blue::before {
      background: #4898ff;
    }
    // dot 灰色
    &.text-info::before,
    &.text-muted::before {
      background: #aaaaaa;
    }
    // dot 红色
    &.dot-red,
    &.text-danger {
      &::before {
        background: #ff4d4f;
      }
    }
  }
  .todo-item-content {
    @apply color-#0C1433 p-30rpx text-26rpx;
    .todo-item-content-item {
      display: flex;
      text {
        width: 140rpx;
        display: inline-block;
        color: #4f555c;
        flex-shrink: 0;
      }
    }
    view + view {
      margin-top: 16rpx;
    }
  }
  .time {
    &.dot-red {
      color: #ff4d4f;
    }
  }
  .bottom-node {
    @apply p-x-30rpx p-y-22rpx color-#6B6B6B text-26rpx;
    border-top: 4rpx solid #f9f9f9;
    .node-box {
      display: flex;
      align-items: center;
      image {
        width: 26rpx;
        height: 26rpx;
        margin-right: 6rpx;
      }
    }
  }
}
</style>
