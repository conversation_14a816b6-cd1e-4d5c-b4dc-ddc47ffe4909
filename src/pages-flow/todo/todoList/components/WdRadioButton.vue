<template>
  <view class="custom-radio">
    <wd-radio-group v-model="queryType" shape="button" @change="handleQuery">
      <wd-radio :value="item.value" v-for="item in options" :key="item.value">
        {{ item.label }} {{ item.count }}
      </wd-radio>
    </wd-radio-group>
  </view>
</template>

<script lang="ts" setup>
defineOptions({
  options: {
    styleIsolation: "shared",
  },
})

const props = defineProps({
  options: {
    type: Array,
    default: () => [],
  },
  current:{ // 当前选中的选项
    required: true,
  }
})

const queryType = ref(props.current)

const emit = defineEmits(["change"])
const handleQuery = (val: number | string) => {
  emit("change", val)
}
</script>

<style lang="scss" scoped>

.custom-radio {
  :deep(.wd-radio-group) {
    background: transparent;
    .wd-radio .wd-radio__label {
      @apply text-26rpx flex-center inline-flex rounded-100rpx h-64rpx leading-64rpx min-w-200rpx;
      border: 1rpx solid $uni-color-primary-light-6;
      background: $uni-color-primary-light-8;
      color: $uni-color-primary;
    }
    .wd-radio.is-checked {
      .wd-radio__label {
        background: var(--wot-color-theme);
        border: 1rpx solid var(--wot-color-theme);
        color: #fff;
      }
    }
  }
}
</style>
