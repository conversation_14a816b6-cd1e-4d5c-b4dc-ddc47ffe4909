<route lang="json5" type="page">
{
  layout: "default",
  needLogin: true,
  style: {
    navigationStyle: "custom",
    navigationBarTitleText: "待办中心",
  },
}
</route>

<script lang="ts" setup>
import CustomTabs from "@/components/custom-tabs/index.vue"
import MineTodoList from "./mineTodoList.vue"
import DoingTodoList from "./doingTodoList.vue"
import OvertimeTodoList from "./overtimeTodoList.vue"
import EndTodoList from "./endTodoList.vue"

defineOptions({
  options: {
    styleIsolation: "shared",
  },
})

const tabOptions = ref([
  {
    label: "待我处理",
    value: 0,
  },
  {
    label: "进行中",
    value: 1,
  },
  {
    label: "即将超时",
    value: 2,
  },
  {
    label: "已完成",
    value: 3,
  },
])

const currentTab = ref(0)
const loading = ref(true)

const changeTabs = (e) => {
  currentTab.value = e.name
}

onLoad((option) => {
  loading.value = true
  currentTab.value = +option.tab || 0
  console.log(currentTab.value)
  loading.value = false
})
</script>

<template>
  <view class="app-container" v-if="!loading">
    <fg-navbar>待办中心</fg-navbar>
    <CustomTabs v-model="currentTab" :tab-options="tabOptions" @change="changeTabs">
    </CustomTabs>
    <MineTodoList v-if="currentTab === 0" />
    <DoingTodoList v-if="currentTab === 1" />
    <OvertimeTodoList v-if="currentTab === 2" />
    <EndTodoList v-if="currentTab === 3" />
  </view>
</template>

<style lang="scss" scoped>
.app-container {
  overflow: hidden;
  height: 100vh;
}
</style>
