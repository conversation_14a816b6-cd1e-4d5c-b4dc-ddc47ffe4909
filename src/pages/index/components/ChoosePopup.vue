<template>
  <wd-overlay :show="modelValue" :z-index="999" @click="visible = false">
    <view class="overlay-wrapper">
      <view class="choose-popup">
        <view class="close-icon">
          <wd-icon name="close" @click="close"></wd-icon>
        </view>
        <view
          v-for="(item, idx) in options"
          :key="idx"
          :class="['choose-popup-item', { active: current === item.value }]"
          @click="select(item)"
        >
          <image :src="getIcon(item)" class="choose-popup-icon" mode="aspectFit" />
          <view class="choose-popup-content">
            <view class="choose-popup-title">{{ item.title }}</view>
            <view class="choose-popup-desc">{{ item.desc }}</view>
          </view>
        </view>
      </view>
    </view>
  </wd-overlay>
</template>

<script setup>
const props = defineProps({
  modelValue: Boolean,
  options: Array, // [{ icon: 'lunch-file', title: '', desc: '' }]
  highlight: Boolean,
})
const emit = defineEmits(["update:modelValue", "select"])

const options = [
  { icon: "lunch-file", title: "手机文件发起", desc: "从手机中选择文件发起", value: 0 },
  { icon: "lunch-wx", title: "微信文件发起", desc: "从微信选择文件发起", value: 1 },
  { icon: "lunch-img", title: "图片发起", desc: "拍照/选择图片发起", value: 2 },
]
const current = ref(0)
function close() {
  emit("update:modelValue", false)
}
function select(item) {
  current.value = item.value
  emit("select", item)
  close()
}
// 拼接图片路径
function getIcon(item) {
  const prefix = "/static/images/icon/"
  return item.value === current.value ? `${prefix}${item.icon}-l.png` : `${prefix}${item.icon}.png`
}
</script>

<style scoped lang="scss">
.overlay-wrapper {
  @apply flex-center w-full h-full;
}
.choose-popup {
  border-radius: 18rpx;
  padding: 80rpx 50rpx 40rpx;
  width: 84%;
  box-sizing: border-box;
  background: linear-gradient(180deg, #daf0e9 0%, #ffffff 15%);
  position: relative;
  .close-icon {
    position: absolute;
    right: 0;
    top: 0;
    color: #d6d9da;
    padding: 20rpx 30rpx;
  }
}
.choose-popup-item {
  display: flex;
  align-items: center;
  padding: 32rpx 48rpx;
  background: $uni-color-primary-light-8;
  border: 2rpx solid $uni-color-primary-light-7;
  border-radius: 18rpx;
  transition: background 0.2s;
  margin-bottom: 20rpx;
  &.active {
    background: $uni-color-primary;
    color: #fff;
    .choose-popup-desc{
      color: #fff;
    }
  }
  .choose-popup-icon {
    width: 54rpx;
    height: 54rpx;
    margin-right: 32rpx;
    flex-shrink: 0;
  }
  .choose-popup-title {
    font-weight: 600;
    font-size: 28rpx;
  }
  .choose-popup-desc {
    font-size: 24rpx;
    color: #6b6b6b;
    margin-top: 8rpx;
  }
}
</style>
