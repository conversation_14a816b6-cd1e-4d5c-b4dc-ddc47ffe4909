<script setup>
import { imageApi } from '@/config/constant'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})
const props = defineProps({
  info: {
    type: Object,
    default: () => ({
      iconPath: '',
      appName: '',
    }),
  },
})

// 判断是否是绝对路径（URL 或 / 开头）
function isAbsoluteUrl(path) {
  return /\.(png|jpe?g|gif|webp|svg|ico)(\?.*)?$/i.test(path)
}

const imagePath = computed(() => {
  const isLocalIcon = isAbsoluteUrl(props.info.iconPath)
  return isLocalIcon
    ? imageApi + props.info.iconPath
    : `/static/images/index-icon/${props.info.iconPath}.png`
})

function handleClick() {
  const { info } = props
  if (+info.appAttr === 1) {
    // TODO: 跳转外链
    window.open(info.programUrl)
  }
  else {
    uni.navigateTo({ url: info.programUrl })
  }
}
</script>

<template>
  <div class="icon-nav" @click="handleClick">
    <image :src="imagePath" mode="aspectFit" />
    <div>{{ info.appName }}</div>
  </div>
</template>

<style scoped lang="scss">
.icon-nav {
  text-align: center;
  font-size: 28rpx;
  width: 100%;
  image {
    width: 65rpx;
    height: 65rpx;
    margin: 10rpx auto 5rpx;
    border-radius: 8rpx;
    transition: all 0.2s;
  }
  div {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
