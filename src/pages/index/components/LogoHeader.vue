<script lang="ts" setup>
defineOptions({
  options: {
    styleIsolation: "shared",
  },
})

// 状态栏高度
const status = ref(0)
// nav高度
const navHeight = ref(0)
// 导航栏高度
const allNavHeight = ref("")

const navStyle = computed(() => ({
  height: allNavHeight.value,
  paddingTop: status.value / 2 + "rpx",
}))

const title = computed(() => {
  return import.meta.env.VITE_APP_TITLE
})

function setNavSize() {
  const app = uni.getSystemInfoSync()
  let statusBarHeight = app.statusBarHeight || 0

  // 默认H5导航高度
  let navBarHeight = 60

  // 微信小程序专用处理
  if (uni.getMenuButtonBoundingClientRect) {
    const menuButtonInfo = uni.getMenuButtonBoundingClientRect()

    // 确保胶囊按钮信息有效
    if (menuButtonInfo && menuButtonInfo.top > 0) {
      // 正确计算导航栏高度（包括状态栏）
      navBarHeight = menuButtonInfo.bottom + menuButtonInfo.top - statusBarHeight
    } else {
      // 备用方案：使用系统导航栏高度
      navBarHeight = app.navigationBarHeight || navBarHeight
    }
  }

  // 转换为rpx
  const pxToRpxRatio = 100 / uni.upx2px(100)
  status.value = statusBarHeight * pxToRpxRatio
  navHeight.value = navBarHeight * pxToRpxRatio
  allNavHeight.value = `${navHeight.value}rpx`
}
onMounted(() => {
  setNavSize()
})
</script>

<template>
  <view class="logo-header" :style="navStyle">
    <image
      src="@/static/images/index/banner-header.png"
      mode="aspectFill"
      class="logo-header-bg"
      :style="{ height: allNavHeight }"
    />
    <view class="logo-header-content">
      <wd-button plain size="small" class="switch-btn z-20">
        <div class="flex-y-center">
          <image
            class="mr-6rpx w-20rpx h-20rpx w-20rpx"
            src="@/static/images/icon/index-user.png"
            mode="aspectFit"
          />
          <text class="leading-50rpx"> 个人版 </text>
        </div>
      </wd-button>
      <div class="logo">
        <image src="@/static/logo.png" mode="aspectFit" />
        <span class="title">
          {{ title }}
        </span>
      </div>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.logo-header {
  z-index: 99;
  @apply fixed flex-center w-100% top-0 right-0 left-0;
  .logo-header-bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
  }
  :deep(.wd-button) {
    position: absolute;
    left: 20rpx;
    background: #c8ece1;
    .wd-button__content {
      display: flex;
      align-items: center;
    }
  }
  .logo-header-content {
    position: relative;
    z-index: 50;
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .logo {
    display: flex;
    align-items: center;
    font-size: 36rpx;
    image {
      height: 50rpx;
      width: 50rpx;
      margin-right: 10rpx;
    }
    .title {
      font-family: "DingTalk", "PingFang", "Microsoft YaHei", "Roboto‌";
    }
  }
}
</style>
