<script setup>
import { querySummaryIndex } from '@/api/home'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})
const isEmpty = ref(false)

const countData = ref({
  finishCount: 0,
  onGoingCount: 0,
  timeOutCount: 0,
  todoCount: 0,
})

const todoList = [
  { key: 'todoCount', text: '待处理', path: '/pages-flow/todo/todoList/index' },
  { key: 'onGoingCount', text: '进行中', path: '/pages-flow/todo/todoList/index?tab=1' },
  { key: 'timeOutCount', text: '即将超时', path: '/pages-flow/todo/todoList/index?tab=2' },
  { key: 'finishCount', text: '已完结' , path: '/pages-flow/todo/todoList/index?tab=3'},
]

async function getData() {
  const res = await querySummaryIndex()
  countData.value = res.object || {}
}

function toTodoPage(path) {
  uni.navigateTo({
    url: path,
  })
}

getData()
</script>

<template>
  <div class="todo-nav">
    <div class="title">
      待办中心
    </div>
    <div v-if="isEmpty" class="empty-tip">
      <image src="@/static/images/list-empty.png" mode="widthFix" />
      <text>清空也是一种快乐~</text>
    </div>
    <div v-if="!isEmpty" class="todo-list">
      <div v-for="item in todoList" :key="item.key" class="todo-item" @click="toTodoPage(item.path)">
        <wd-count-to
          class="todo-count"
          :font-size="28"
          color="#212939"
          :start-val="0"
          :end-val="countData[item.key] || 0"
          :duration="1000"
        />
        <div class="todo-text">
          {{ item.text }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.todo-nav {
  margin-top: 30rpx;
  .title {
    @apply title-line mb-20rpx;
  }
  .empty-tip {
    background: linear-gradient(180deg, #ebf6f2 0%, #ffffff 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    image {
      width: 165rpx;
    }
    text {
      color: #84ad9e;
      padding-bottom: 18rpx;
    }
  }
  .todo-list {
    display: flex;
    justify-content: space-around;
    background: linear-gradient(180deg, #ebf6f2 0%, #ffffff 100%);
    border-radius: 19rpx;
    padding: 15rpx 20rpx;
    .todo-item {
      text-align: center;
      :deep(text) {
        font-family: bahnschrift, '微软雅黑';
      }
      .todo-count {
        font-size: 54rpx;
        display: inline-block;
      }
      .todo-text {
        font-size: 26rpx;
      }
    }
  }
}
</style>
