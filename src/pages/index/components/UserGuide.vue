<script lang="ts" setup>
function toManualPage() {
  uni.navigateTo({
    url: '/pages/other/manualList/index',
  })
}
</script>

<template>
  <view class="guide-container" @click="toManualPage">
    <view class="guide-content">
      <view class="guide-main">
        <view class="guide-title">
          <span class="relative z20">用户指南</span>
        </view>
        <view class="text-24rpx">
          常见问题答疑，使用更便捷
        </view>
      </view>
      <image
        class="guide-img"
        src="@/static/images/index/guide-img.png"
        mode="widthFix"
      />
    </view>
    <image class="bg" src="@/static/images/index/guide-bg.png" mode="scaleToFill" />
  </view>
</template>

<style lang="scss" scoped>
.guide-container {
  position: relative;
  height: 160rpx;
  margin-top: 20rpx;

  .bg {
    height: 100%;
    width: 100%;
  }
  .guide-content {
    @apply flex-x-between absolute z20 left-0 right-0 top-0 w-85% h-100% m-auto;
    .guide-img {
      width: 130rpx;
      margin-right: 30rpx;
    }
    .guide-title {
      @apply title-line-b  before:(bg-#7BBBF4) text-40rpx font-600 mb-10rpx;
      font-family: 'DingTalk';
    }
  }
}
</style>
