<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  layout: "tabbar",
  style: {
    // 'custom' 表示开启自定义导航栏，默认 'default'
    navigationStyle: "custom",
    navigationBarTitleText: "首页",
    backgroundColor: '#fff',
  },
}
</route>

<script lang="ts" setup>
import NoRealTip from '@/components/no-real-tip/no-real-tip.vue'
import { useUserStore } from '@/store/index'
import LogoHeader from './components/LogoHeader.vue'
import LunchNav from './components/LunchNav.vue'
import ModuleNav from './components/ModuleNav.vue'
import TodoNav from './components/TodoNav.vue'
import UserGuide from './components/UserGuide.vue'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})

const isUnReal = computed(() => {
  return +useUserStore().userInfo.izRealAuth !== 1 || false
})
</script>

<template>
  <view class="app-container">
    <LogoHeader />
    <div class="banner-container">
      <image class="banner" src="/static/images/index/banner.png" />
    </div>

    <!-- 实名提醒 -->
    <div v-if="isUnReal" class="un-real">
      <NoRealTip />
    </div>

    <!-- 主体内容 -->
    <div v-if="!isUnReal" class="index-main">
      <!-- 发起 -->
      <LunchNav />
      <UserGuide />
      <!-- 待办 -->
      <TodoNav />
      <!-- 模块 -->
      <ModuleNav />
    </div>
  </view>
</template>

<style lang="scss" scoped>
@import url('@/assets/fonts/font.scss');

.app-container {
  min-height: 98vh;
  background: #fff;
}

.banner-container {
  width: 100%;
  height: 550rpx;
  .banner {
    width: 100%;
    height: 100%;
  }
}

.index-main {
  padding: 30rpx 30rpx 100rpx;
}
.un-real {
  padding: 30rpx 30rpx 100rpx;
  height: 570rpx;
  overflow: hidden;
}
</style>
