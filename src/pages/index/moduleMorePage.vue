<route lang="json5" type="page">
{
  layout: "default",
  needLogin: true,
  style: {
    navigationBarTitleText: "电子印章平台",
    navigationStyle: "custom",
  },
}
</route>

<template>
  <view class="app-container">
    <fg-navbar>电子印章平台</fg-navbar>
    <view class="module-nav-item-list">
      <IconNav class="item-card" v-for="item in dataList" :info="item" :key="item.id" />
    </view>
    <view class="p-30rpx relative z-10" v-if="loading">
      <wd-skeleton
        theme="image"
        :row-col="[
          [
            { width: '20%', height: '48px' },
            { width: '20%', height: '48px' },
            { width: '20%', height: '48px' },
            { width: '20%', height: '48px' },
          ],
          [
            { width: '20%', height: '48px' },
            { width: '20%', height: '48px' },
            { width: '20%', height: '48px' },
            { width: '20%', height: '48px' },
          ],
        ]"
      />
    </view>
  </view>
</template>

<script lang="ts" setup>
import { getHomeModuleList } from "@/api/home/<USER>"
import IconNav from "./components/IconNav.vue"

const dataList = ref([])
const loading = ref(true)
function getList(moduleType) {
  getHomeModuleList({ moduleType, appType: 1 })
    .then((res) => {
      dataList.value = res.object
    })
    .finally(() => {
      loading.value = false
    })
}

onLoad((option) => {
  getList(option.moduleType)
})
</script>

<style lang="scss" scoped>
.module-nav-item-list {
  display: flex;
  flex-wrap: wrap;
  position: relative;
  z-index: 99;
  margin-top: 20rpx;
  .item-card {
    width: 24%;
    margin-right: 1%;
    flex-shrink: 0;
    margin-bottom: 20rpx;
    font-size: 26rpx;
  }
}
</style>
