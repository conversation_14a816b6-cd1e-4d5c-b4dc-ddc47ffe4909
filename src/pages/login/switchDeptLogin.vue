<route lang="json5">
{
style: {
navigationStyle: "custom",
navigationBarTitleText: "角色切换",
},
}
</route>

<script setup lang="ts">
import { zsLogin } from '@/api/login'
import RoleList from '@/components/role-list/role-list.vue'
import { imageApi } from '@/config/constant'
import { useUserStore } from '@/store'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})

const loading = ref(false)
// 用户信息
const userInfo = useUserStore().userInfo

// 确认角色登录
async function handleConfirmLogin() {
  try {
    if(!userInfo.deptId) return
    loading.value = true
    const res = await zsLogin({
     username: userInfo.userName,
     loginDeptId: userInfo.deptId,
     password: userInfo.password
    })
    useUserStore().updateToken(res.object.access_token)
    useUserStore().getUserInfo()
    uni.switchTab({ url: '/pages/index/index' })
  } catch (error) {
    console.error('确认角色登录失败', error)
  }finally{
    loading.value = false
  }
}
</script>

<template>
  <view class="switch-role-container">
    <fg-navbar>角色切换</fg-navbar>

    <view class="user-info-section">
      <view class="avatar-container">
        <image class="avatar" :src="imageApi + userInfo.avatar" mode="aspectFill" />
      </view>
      <view class="greeting">
        Hi，{{ userInfo.nickName }}
      </view>
      <view class="description">
        系统检测到您的账号归属多个部门，请先切换对应的部门以及角色
      </view>
    </view>

    <view class="role-section">
      <RoleList v-model="userInfo.deptId " />
    </view>

    <view class="bottom-action">
      <wd-button
        block
        @click="handleConfirmLogin"
        :disabled="loading"
        :loading="loading"
      >
        以此角色登录
      </wd-button>
    </view>
  </view>
</template>

<style scoped lang="scss">
.switch-role-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.user-info-section {
  padding: 60rpx 40rpx 40rpx;
  text-align: center;
  margin-bottom: 20rpx;

  .avatar-container {
    margin-bottom: 24rpx;

    .avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      background-color: #f0f0f0;
    }
  }

  .greeting {
    font-size: 36rpx;
    font-weight: 600;
    color: rgba(12, 20, 51, 1);
    margin-bottom: 20rpx;
  }

  .description {
    font-size: 27rpx;
    color: rgba(56, 56, 58, 1);
    line-height: 1.6;
    padding: 0 20rpx;
    text-align: start;
  }
}

.role-section {
  height: 620rpx;
  padding: 0 40rpx;
  margin-bottom: 40rpx;
}

.bottom-action {
  padding: 35rpx 120rpx 45rpx 120rpx;

  :deep(.wd-button) {
    height: 95rpx !important;
  }
}
</style>
