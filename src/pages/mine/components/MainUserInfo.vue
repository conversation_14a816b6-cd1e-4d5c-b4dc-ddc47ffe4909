<script lang="ts" setup>
import { imageApi } from '@/config/constant'
import SwitchRolePopup from './switchRolePopup.vue'

const props = defineProps<{
  userInfo: any
}>()
const switchRolePopup = ref()
function toPersonalInfo() {
  uni.navigateTo({ url: '/pages/mine/editPersonalInfo' })
}

// 切换部门弹窗
function toSwitchRole() {
  switchRolePopup.value.open()
}
// 是否能切换部门标记
const isSingleDept = computed(() => {
  return props.userInfo && props.userInfo.deptRoleInfos.length > 1 || false
})
</script>

<template>
  <view class="mine-header">
    <image class="mine-header-bg" src="@/static/images/mine/mine-bg.png" mode="widthFix" />

    <view class="mine-header-main">
      <view class="mine-header-info" @click="toPersonalInfo">
        <image class="user-avatar" :src="imageApi + userInfo.avatar" mode="aspectFit" />
        <view class="mine-header-info-name">
          <view class="name">
            {{ userInfo.nickName }}
            <wd-icon class="ml-5rpx" name="arrow-right" size="30rpx" />
          </view>

          <view v-if="+userInfo.izRealAuth === 1" class="real">
            <image src="@/static/images/icon/real-name.png" mode="widthFix" />
            已实名认证
          </view>
          <view v-if="+userInfo.izRealAuth !== 1" class="real no-real">
            <image src="@/static/images/icon/un-real.png" mode="widthFix" />
            未实名，
            <text class="color-#FFBF2E underline">
              实名链接推送
            </text>
          </view>
        </view>
      </view>

      <view class="user-belong">
        <image class="dept-icon" src="@/static/images/mine/dept-icon.png" mode="widthFix" />
        <view class="name">
          中山大学
        </view>
      </view>

      <view v-if="userInfo.loginRoles" class="role-box">
        <span v-for="(item, index) in userInfo.loginRoles" style="font-size:25rpx;">
          {{ item.roleName }}
          <wd-divider v-if="index !== userInfo.loginRoles.length - 1" vertical color="#FFFFFF" />
        </span>
        <wd-icon v-if="isSingleDept" class="ml-10rpx font-600" name="swap" size="28rpx" @click="toSwitchRole" />
      </view>
    </view>

    <!-- 角色切换弹窗 -->
    <SwitchRolePopup ref="switchRolePopup" />
  </view>
</template>

<style lang="scss" scoped>
.mine-header {
  position: relative;
  color: #fff;
  top: -10rpx;

  .mine-header-bg {
    width: 100%;
    height: 700rpx;
  }

  .mine-header-main {
    position: absolute;
    top: 12vh;
    left: 40rpx;
  }

  .mine-header-info {
    display: flex;
    align-items: center;

    .user-avatar {
      width: 120rpx;
      height: 120rpx;
      flex-shrink: 0;
      border-radius: 50%;
      border: 4rpx solid #fff;
      margin-right: 20rpx;
    }

    .mine-header-info-name {
      .name {
        font-size: 36rpx;
        margin-bottom: 10rpx;
      }

      .real {
        display: flex;
        align-items: center;
        background: #2a916f;
        border-radius: 9rpx;
        padding: 6rpx 16rpx;
        font-size: 26rpx;

        image {
          width: 25rpx;
          height: 25rpx;
          margin-right: 10rpx;
        }
      }
    }
  }

  .user-belong {
    margin-top: 35rpx;
    display: flex;
    align-items: center;
    font-size: 32rpx;

    .dept-icon {
      width: 42rpx;
      height: 42rpx;
      flex-shrink: 0;
      margin-right: 20rpx;
    }
  }

  .role-box {
    margin-top: 20rpx;
    --wot-divider-vertical-height: 20rpx;
  }
}
</style>
