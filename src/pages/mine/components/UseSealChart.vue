<script setup lang="ts">
import uCharts from '@qiun/ucharts/u-charts'
import NoRealTip from '@/components/no-real-tip/no-real-tip.vue'

const props = defineProps<{
  userInfo: any
}>()

const signedCount = ref(121)
const pendingCount = ref(42)

const canvasId = 'sealChart'

const totalCount = computed(() => signedCount.value + pendingCount.value)

// 和绑定canvas的CSS一样 确保会显示
const cWidth = ref(320)
const cHeight = ref(320)
const uChartsInstance = ref({})
const { proxy } = getCurrentInstance()

const signedPercent = computed(() =>
  Math.round((signedCount.value / totalCount.value) * 100),
)

const pendingPercent = computed(() =>
  Math.round((pendingCount.value / totalCount.value) * 100),
)
function getChartData() {
  return [
    { name: '已签署', value: signedCount.value, color: '#03C18A' },
    { name: '待签署', value: pendingCount.value, color: '#5B82FF' },
  ]
}

// 初始化图表
function initChart() {
  const query = uni.createSelectorQuery().in(proxy)
  query.select(`#${canvasId}`).fields({ node: true, size: true }, (data) => {
    const canvas = data.node
    const ctx = canvas.getContext('2d')
    const dpr = uni.getSystemInfoSync().pixelRatio
    canvas.width = data.width * dpr
    canvas.height = data.height * dpr

    let chartWidth = cWidth.value
    let chartHeight = cHeight.value

    // #ifdef H5
    ctx.scale(dpr, dpr)
    // #endif

    // #ifndef H5
    chartWidth = cWidth.value * dpr
    chartHeight = cHeight.value * dpr
    // #endif

    uChartsInstance.value[canvasId] = new uCharts({
      type: 'ring',
      context: ctx,
      width: chartWidth,
      height: chartHeight,
      pixelRatio: dpr,
      series: [
        { data: [{ name: '已签署', value: 121, color: '#03C18A', select: true }, { name: '待签署', value: 50, color: '#5B82FF' }] },
      ],
      animation: true,
      rotate: false,
      rotateLock: false,
      dataLabel: false,
      background: '#FFFFFF',
      color: ['#03C18A', '#5B82FF'],
      padding: [0, 0, 0, 0],
      enableScroll: false,
      legend: {
        show: false,
      },
      tooltip: { show: true },
      title: {
        name: '163',
        fontSize: 22,
        color: '#222222',
        offsetY: -6,
      },
      subtitle: {
        name: '已签署',
        fontSize: 12,
        color: '#5C5C5C',
      },
      extra: {
        ring: {
          ringWidth: 26,
          activeOpacity: 1,
          offsetAngle: 0,
          labelWidth: 0,
          border: false,
          borderWidth: 0,
          borderColor: '#FFFFFF',
          defaultSelected: 0,
        },
      },
    })
    // 强制显示索引
  }).exec()
}

// 更新图表
function updateChart() {

}

// 监听数据变化
watch([signedCount, pendingCount], () => {
  updateChart()
}, { immediate: false })

onMounted(() => {
  cWidth.value = uni.upx2px(cWidth.value)
  cHeight.value = uni.upx2px(cHeight.value)
  nextTick(() => {
    initChart()
  })
})

onUnmounted(() => {
  if (uChartsInstance.value) {
    uChartsInstance.value = null
  }
})
</script>

<template>
  <view class="seal-chart-card">
    <view :v-show="+userInfo.izRealAuth === 1">
      <view class="chart-title">
        文件用印情况
      </view>

      <view class="flex-center">
        <view class="charts-box">
          <canvas :id="canvasId" :canvas-id="canvasId" type="2d" class="charts-canvas" />
        </view>
      </view>

      <view class="stats-container">
        <view class="stat-card signed">
          <view class="stat-header">
            <view class="stat-indicator" />
            <text class="stat-label">
              已签署
            </text>
          </view>
          <view class="stat-numbers">
            <text>{{ signedCount }}</text>
            <text>{{ signedPercent }}%</text>
          </view>
        </view>

        <view class="stat-card pending">
          <view class="stat-header">
            <view class="stat-indicator" />
            <text class="stat-label">
              待签署
            </text>
          </view>
          <view class="stat-numbers">
            <text>{{ pendingCount }}</text>
            <text>{{ pendingPercent }}%</text>
          </view>
        </view>
      </view>
    </view>
    <NoRealTip v-if="+userInfo.izRealAuth !== 1 && +userInfo" />
  </view>
</template>

<style lang="scss" scoped>
.seal-chart-card {
  margin: 32rpx;
  border-radius: 20rpx;
  background: #ffffff;
  padding: 40rpx 30rpx;
}

.chart-title {
  font-size: 32rpx;
  width: fit-content;
  font-weight: 600;
  color: #333333;
  border-radius: 16rpx;
  position: relative;
  z-index: 2;

  &::after {
    content: '';
    z-index: -1;
    position: absolute;
    bottom: 4rpx;
    width: 100%;
    left: 0;
    height: 40rpx;
    background: linear-gradient(to top, $uni-color-primary 0%, #fff 50%);
    filter: blur(5px);
    opacity: 0.55;
  }
}

.charts-box {
  width: 320rpx;
  height: 320rpx;
  margin: 30rpx 0;
}

.charts-canvas {
  width: 320rpx;
  height: 320rpx;
}

.stats-container {
  display: flex;
  gap: 16px;
}

.stat-card {
  flex: 1;
  border-radius: 18rpx;
  padding: 24rpx 40rpx 24rpx 32rpx;

  &.signed {
    background: rgba(27, 196, 125, 0.1);
  }

  &.pending {
    background: rgba(91, 143, 249, 0.1);
  }
}

.stat-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20rpx;
}

.stat-indicator {
  width: 32rpx;
  height: 32rpx;
  border-radius: 6rpx;

  .stat-card.signed & {
    background: #03c18a;
  }

  .stat-card.pending & {
    background: #5b82ff;
  }
}

.stat-label {
  font-size: 28rpx;
  color: #0c1433;
}

.stat-numbers {
  font-family: bahnschrift, '微软雅黑';
  color: #212939;
  font-size: 42rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 48rpx;
}
</style>
