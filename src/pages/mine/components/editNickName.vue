<script lang="ts" setup>
import { updateUserProfile } from '@/api/system/user'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})
const emit = defineEmits<{
  success
}>()
const visible = ref(false)
const userInfo = ref({})
const formRef = ref()
const rules = {
  nickName: [
    {
      required: true,
      message: '用户名不能为空',
      trigger: 'blur',
    },
    {
      validator: checkSpecialChar,
      trigger: 'blur',
    },
  ],
}
// 打开弹窗
function open(nickName) {
  userInfo.value.nickName = nickName
  visible.value = true
}

// 特殊字符校验
function checkSpecialChar(value) {
  const reg = /^[\u4E00-\u9FA5\w+\-()（）]+$/
  if (!reg.test(value)) {
    return Promise.reject('用户名中不允许包含特殊字符')
  }
  else {
    return Promise.resolve()
  }
}

// 关闭弹窗

// 提交修改
async function handleConfirm() {
  try {
    const valid = await formRef.value.validate()
    if (!valid.valid)
      return
    const res = await updateUserProfile(userInfo.value)
    visible.value = false
    emit('success')
  }
  catch (error) {
    console.error(error)
  }
}

defineExpose({ open })
</script>

<template>
  <view>
    <wd-popup v-model="visible" custom-class="switch-popup">
      <wd-form ref="formRef" :model="userInfo" :rules="rules">
        <wd-input v-model="userInfo.nickName" prop="nickName" placeholder="请输入姓名" clearable :maxlength="16" focus />
        <view class="footer">
          <wd-button custom-class="btn-cancel" type="info" @click="visible = false">
            取消
          </wd-button>
          <wd-button custom-class="btn-confirm" type="primary" @click="handleConfirm">
            保存
          </wd-button>
        </view>
      </wd-form>
    </wd-popup>
  </view>
</template>

<style lang="scss" scoped>
.footer {
  display: flex;
  justify-content: space-around;
}
:deep() {
  .switch-popup {
    padding: 40rpx !important;
    width: 610rpx;
    border-radius: 16rpx;
    background: linear-gradient(to bottom, $uni-color-primary-light-6 0%, #ffffff 6%) !important;
  }
  .wd-input {
    margin-bottom: 45rpx;
    padding: 0 40rpx;
    height: 90rpx;
    border-radius: 60rpx;
    border: $uni-color-primary-light-8 1rpx solid;
  }

  .wd-input__value {
    height: 100% !important;
  }

  .wd-input__body {
    height: 100% !important;
  }

  .wd-input.is-not-empty::after,
  .wd-input:not(.is-disabled)::after {
    display: none !important;
    content: none !important;
  }
}
</style>
