<script setup lang="ts">
import { switchRoleLogin } from '@/api/login'
import RoleList from '@/components/role-list/role-list.vue'
import { useUserStore } from '@/store'
import { toast } from '@/utils/toast'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})

const visible = ref(false)

// 打开弹窗
function open() {
  visible.value = true
}

const userInfo = computed(() => {
  return useUserStore().userInfo
})

// 确认
async function handleConfirm() {
  try {
    uni.showLoading({
      title: '正在切换角色中...',
      mask: true,
    })
    let res = await switchRoleLogin({ userId: userInfo.value.userId, switchDeptId: userInfo.value.deptId })
    useUserStore().updateToken(res.object.access_token)
    useUserStore().getUserInfo()
    if (+res.code === 200) {
      toast.success('角色切换成功')
      uni.reLaunch({ url: '/pages/index/index' })
    }
  }
  catch (error) {
    console.error('角色切换失败', error)
  }
  finally {
    visible.value = false
    uni.hideLoading()
  }
}

defineExpose({ open })
</script>

<template>
  <view>
    <wd-popup v-model="visible" custom-class="switch-popup" :close-on-click-modal="false">
      <wd-icon name="close" color="#D6DADB" size="12" custom-class="btn-close" @click="visible = false" />
      <view class="popup-title">
        部门切换
      </view>

      <view class="popup-content">
        <RoleList v-model="userInfo.deptId" />
      </view>

      <view class="popup-action">
        <wd-button custom-class="btn-cancel" type="info" @click="visible = false">
          取消
        </wd-button>
        <wd-button custom-class="btn-confirm" type="primary" @click="handleConfirm">
          确认
        </wd-button>
      </view>
    </wd-popup>
  </view>
</template>

<style scoped lang="scss">
:deep() {
  .switch-popup {
    padding: 40rpx !important;
    width: 610rpx;
    max-height: 1100rpx !important;
    border-radius: 16rpx;
    background: linear-gradient(to bottom, $uni-color-primary-light-6 0%, #ffffff 6%) !important;
  }

  .btn-close {
    position: absolute;
    right: 30rpx;
    top: 30rpx;
  }

  .btn-cancel {
    color: #38383a !important;
    background: #f2f2f2;
    width: 300rpx;
    height: 85rpx !important;
  }

  .btn-confirm {
    width: 300rpx;
    height: 85rpx !important;
  }
}

.popup-title {
  display: flex;
  justify-content: space-between;
  color: #0c1433;
  height: 50rpx;
  font-size: 34rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  margin-top: 2rpx;
}

.popup-content {
  padding: 20rpx 0;
  max-height: 885rpx;
  overflow: hidden;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6rpx;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3rpx;
  }

  &::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 3rpx;

    &:hover {
      background: transparent;
    }
  }
}

.popup-action {
  bottom: 10rpx;
  padding-top: 20rpx;
  display: flex;
  justify-content: space-around;
}
</style>
