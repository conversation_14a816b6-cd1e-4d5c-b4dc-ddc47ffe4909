<route lang="json5" type="page">
{
  layout: "default",
  style: {
    navigationStyle: "custom",
    navigationBarTitleText: "详情",
  },
}
</route>

<script lang="ts" setup>
import { getManualCfgInfoAPI } from '@/api/ess/doc/manualCfgInfo'

const loading = ref(true)
const form = ref({ id: null })
async function init() {
  try {
    const res = await getManualCfgInfoAPI(form.value.id)
    console.log(res)
    form.value = res.object
  }
  finally {
    loading.value = false
  }
}

onLoad((option) => {
  console.log(option)
  form.value.id = option.id
  init()
})
</script>

<template>
  <view class="app-container">
    <fg-navbar>详情</fg-navbar>
  </view>
</template>

<style lang="scss" scoped>
//
</style>
