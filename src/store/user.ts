import type { IUserInfoVo } from '@/api/types/login'
import { defineStore } from 'pinia'
import { tabbarStore } from "@/layouts/fg-tabbar/tabbar"
import { ref } from 'vue'
import {
  getUserInfo as _getUserInfo,
  login as _login,
  logout as _logout,
  wxLogin as _wxLogin,
  getWxCode,
} from '@/api/login'
import { toast } from '@/utils/toast'

// 初始化状态
const userInfoState: IUserInfoVo = {
  username: '',
  avatar: '/static/images/default-avatar.png',
  token: '',
  short_token: '',
}

export const useUserStore = defineStore(
  'user',
  () => {
    // 定义用户信息
    const userInfo = ref<IUserInfoVo>({ ...userInfoState })
    // 设置用户信息
    const setUserInfo = (val: IUserInfoVo) => {
      userInfo.value = { ...userInfoState, ...val, token: val.token || userInfo.value.token }
    }
    // 删除用户信息
    const removeUserInfo = () => {
      userInfo.value = { ...userInfoState }
      uni.removeStorageSync('userInfo')
      uni.removeStorageSync('token')
    }
    /**
     * 获取用户信息
     */
    const getUserInfo = async () => {
      const res = await _getUserInfo()
      const userInfo = res.user
      setUserInfo(userInfo)
      uni.setStorageSync('userInfo', userInfo)
      // TODO 这里可以增加获取用户路由的方法 根据用户的角色动态生成路由
      return res
    }
    /**
     * 用户登录
     * @param credentials 登录参数
     * @returns R<IUserLogin>
     */
    const login = async (credentials: {
      username: string
      password: string
      code: string
      uuid: string
    }) => {
      const res = await _login(credentials)
      const { access_token, zsUserInfo, short_token } = res.object
      userInfo.value.token = access_token
      userInfo.value.short_token = short_token
      getUserInfo()
      // 无加密储存密码
      userInfo.value.password = credentials.password

      if (zsUserInfo.deptList && zsUserInfo.deptList.length > 1) {
        uni.navigateTo({ url: '/pages/login/switchDeptLogin' })
      }
      else {
        uni.switchTab({ url: '/pages/index/index' })
      }
      return res
    }
    /**
     * 更新凭证
     */
    const updateToken = async (token: IUserInfoVo) => {
      userInfo.value.token = token
    }
    /**
     * 退出登录 并 删除用户信息
     */
    const logout = async () => {
      tabbarStore.setCurIdx(0)
      _logout()
      removeUserInfo()
      uni.reLaunch({ url: '/pages/login/index' })
    }
    /**
     * 微信登录
     */
    const wxLogin = async () => {
      // 获取微信小程序登录的code
      const data = await getWxCode()
      const res = await _wxLogin(data)
      await getUserInfo()
      return res
    }

    return {
      userInfo,
      login,
      wxLogin,
      getUserInfo,
      logout,
      updateToken,
      removeUserInfo
    }
  },
  {
    persist: true,
  },
)
