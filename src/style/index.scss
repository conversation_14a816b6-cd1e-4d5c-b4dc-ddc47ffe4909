@import './iconfont.css';
@import './wot.scss';

$textColor: #212939;

:root,
page {
  // 修改按主题色
  // --wot-color-theme: #24a87e;
  // 修改按钮背景色
  // --wot-button-primary-bg-color: green;
  color: $textColor;
  font-family:
    PingFang SC,
    Microsoft YaHei,
    Arial,
    sans-serif;
}

page,
.uni-page {
  background-color: #f9f9f9;
}

page {
  font-size: 28rpx;
}

.uni-tabbar-border {
  background-color: transparent !important;
  height: 0 !important;
  border: none !important;
}
.uni-tabbar {
  box-shadow: 0px -6px 16px 1px rgba(0, 98, 72, 0.05);
}

.text-warning {
  color: $uni-color-warning;
}
.text-danger {
  color: $uni-color-error;
}
