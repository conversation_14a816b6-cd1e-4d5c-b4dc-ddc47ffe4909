// 全局要用的类型放到这里

declare global {
  interface IResData<T> {
    code: number
    msg: string
    object: T
  }

  // uni.uploadFile文件上传参数
  interface IUniUploadFileOptions {
    file?: File
    files?: UniApp.UploadFileOptionFiles[]
    filePath?: string
    name?: string
    formData?: any
  }

  interface IUserInfo {
    nickName?: string
    avatar?: string
    /** 微信的 openid，非微信没有这个字段 */
    openid?: string
    token?: string,
    phonenumber?: string,
    workNumber?: string,
    userName?: string,
  }
}

export {} // 防止模块污染
