// https://www.npmjs.com/package/@uni-helper/unocss-preset-uni
import { presetUni } from '@uni-helper/unocss-preset-uni'
import {
  defineConfig,
  presetAttributify,
  presetIcons,
  transformerDirectives,
  transformerVariantGroup,
} from 'unocss'

export default defineConfig({
  presets: [
    presetUni({
      attributify: {
        // prefix: 'fg-', // 如果加前缀，则需要在代码里面使用 `fg-` 前缀，如：<div fg-border="1px solid #000"></div>
        prefixedOnly: true,
      },
    }),
    presetIcons({
      scale: 1.2,
      warn: true,
      extraProperties: {
        'display': 'inline-block',
        'vertical-align': 'middle',
      },
    }),
    // 支持css class属性化
    presetAttributify(),
  ],
  transformers: [
    // 启用指令功能：主要用于支持 @apply、@screen 和 theme() 等 CSS 指令
    transformerDirectives(),
    // 启用 () 分组功能
    // 支持css class组合，eg: `<div class="hover:(bg-gray-400 font-medium) font-(light mono)">测试 unocss</div>`
    transformerVariantGroup(),
  ],
  theme: {
    colors: {
      /** 主题色，用法如: text-primary */
      primary: '#24A87E',
    },
    fontSize: {
      /** 提供更小号的字体，用法如：text-2xs */
      '2xs': ['20rpx', '28rpx'],
      '3xs': ['18rpx', '26rpx'],
    },
  },
  safelist: [],
  rules: [
    [
      'p-safe',
      {
        padding:
          'env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left)',
      },
    ],
    ['pt-safe', { 'padding-top': 'env(safe-area-inset-top)' }],
    ['pb-safe', { 'padding-bottom': 'env(safe-area-inset-bottom)' }],
    [
      /^dot-(\d+)-(.+)$/, // 匹配类名：dot-{size}-{color}
      ([, size, color]) => ({
        'width': `${size}rpx`,
        'height': `${size}rpx`,
        'background-color': color.startsWith('#') ? color : '#24A87E', // 支持颜色名或 hex 值
      }),
    ],
    [
      /^title-line-(\d+)-(.+)$/, // 匹配类名：title-line-{size}-{color}
      ([, size, color]) => ({
        'height': `${size}rpx`,
        'background-color': color.startsWith('#') ? color : '#24A87E', // 支持颜色名或 hex 值
      }),
    ],
  ],
  shortcuts: [
    {
      'center': 'flex justify-center items-center',
      'flex-center': 'flex justify-center items-center',
      'flex-x-center': 'flex justify-center',
      'flex-y-center': 'flex items-center',
      'flex-x-between': 'flex items-center justify-between',
      'flex-x-end': 'flex items-center justify-end',
      'text-overflow': 'truncate',
      'bg-no-repeat-cover': 'bg-no-repeat bg-cover',
      'abs-x-center': 'absolute left-50% top-0 translate-x--1/2',
      'abs-y-center': 'absolute left-0 top-50% translate-y--1/2',
      'abs-center': 'absolute left-50% top-50% translate-x--1/2 translate-y--1/2',
      'dot': 'relative pl-24rpx before:(content-empty absolute left-0 top-1/2 -translate-y-1/2 rounded-full dot-12-primary)',
      'title-line-b': 'relative font-bold inline-block leading-40rpx before:(content-empty absolute left-0 bottom-[-2rpx] w-100% h-16rpx block )',
      'title-line':
        'relative font-600 pl-12px before:(content-empty absolute left-0 top-1/2 -translate-y-1/2 rounded-5px w-7rpx title-line-26-primary)',
    },
  ],
})
